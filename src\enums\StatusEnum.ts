export enum StatusEnum {
  SentByDoctor = "sent_by_doctor",
  ApprovedBySpecialist = "approved_by_specialist",
  RejectedBySpecialist = "rejected_by_specialist",
  SentBySpecialist = "sent_by_specialist",
  ApprovedByDoctor = "approved_by_doctor",
  RejectedByDoctor = "rejected_by_doctor",
  Accepted = "accepted",
  InWorking = "in_working",
  Completed = "completed",
  CancelledByAdmin = "cancelled_by_admin",
}

// For labels:
export const StatusLabels: Record<StatusEnum, string> = {
  [StatusEnum.SentByDoctor]: "Sent By Doctor",
  [StatusEnum.ApprovedBySpecialist]: "Approved By Specialist",
  [StatusEnum.RejectedBySpecialist]: "Rejected By Specialist",
  [StatusEnum.SentBySpecialist]: "Sent By Specialist",
  [StatusEnum.ApprovedByDoctor]: "Approved By Doctor",
  [StatusEnum.RejectedByDoctor]: "Rejected By Doctor",
  [StatusEnum.Accepted]: "Accepted",
  [StatusEnum.InWorking]: "In Working",
  [StatusEnum.Completed]: "Completed",
  [StatusEnum.CancelledByAdmin]: "Cancelled By Admin",
};
