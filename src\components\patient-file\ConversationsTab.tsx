import React, { useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";
import { getToken, decryptToken } from "@/app/lib/auth";
import { FaUserMd, FaUserNurse } from "react-icons/fa";
import { ImSpinner2 } from "react-icons/im";
import { FiPaperclip } from "react-icons/fi";
import DeleteConfirmationModal from "../reuseable/DeleteConfirmationModal";
import Image from "next/image";
import { API_ROUTES } from "@/utils/ApiRoutes";
import { getDecryptedToken } from "@/app/lib/auth";

// Set SOCKET_URL to match backend - try port 5000 like working HTML client
const SOCKET_URL =
  process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:5000";

// Cloud storage base URL for files
const CLOUD_STORAGE_BASE_URL =
  "https://orthodontivc-20250724-oss.oss-me-central-1.aliyuncs.com";

// Function to construct proper file URL
const getFileUrl = (fileUrl: string | undefined): string => {
  if (!fileUrl) return "";

  // If already absolute URL, return as is
  if (fileUrl.startsWith("http://") || fileUrl.startsWith("https://")) {
    console.log("🔗 Absolute URL detected:", fileUrl);
    return fileUrl;
  }

  // If relative path, construct absolute URL
  const cleanPath = fileUrl.startsWith("/") ? fileUrl : `/${fileUrl}`;
  const fullUrl = `${CLOUD_STORAGE_BASE_URL}${cleanPath}`;
  console.log("🔗 Constructed URL:", {
    original: fileUrl,
    cleanPath,
    fullUrl,
  });
  return fullUrl;
};

console.log("SOCKET_URL being used:", SOCKET_URL);

const MAX_FILE_SIZE = 50 * 1024 * 1024;
const ALLOWED_FILE_TYPES = [
  "application/zip",
  "application/x-zip",
  "application/x-zip-compressed",
  "application/octet-stream",
  "application/pdf",
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
  "image/svg+xml",
];

// Add props interface
export interface ConversationsTabProps {
  currentUserId: number;
  patientId: number;
  doctorsId: number;
}

// Define Message interface
export interface Message {
  id: number;
  sender_id: number;
  patient_id: number;
  message: string;
  file_url?: string;
  file_name?: string;
  file_type?: string;
  file_size?: number;
  created_at: string;
  sender_name?: string; // Add this
  receiver_name?: string;
  // Add any other fields as needed, like first_name, last_name if available from server for new messages
}

const ConversationsTab: React.FC<ConversationsTabProps> = ({
  currentUserId,
  patientId,
  doctorsId,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageInput, setMessageInput] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<
    React.ReactElement<{ src: string }> | string | null
  >(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // Removed unused socket state
  const [receiverId, setReceiverId] = useState<number | null>(null);
  const [receiverLoading, setReceiverLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false); // Add uploading state
  const [pendingMessage, setPendingMessage] = useState<{
    text: string;
    file: File | null;
    filePreview: React.ReactNode;
  } | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [messageToDelete, setMessageToDelete] = useState<number | null>(null);
  const [errorModalOpen, setErrorModalOpen] = useState(false);
  const [errorModalMessage, setErrorModalMessage] = useState("");
  const [messagesLoading, setMessagesLoading] = useState(true);
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
  const [expandedMessages, setExpandedMessages] = useState<{
    [id: number]: boolean;
  }>({});
  const [userRole, setUserRole] = useState<string | null>(null);
  const userId = getDecryptedToken("userid");

  // Name states for displaying actual names
  const [doctorName, setDoctorName] = useState<string>("");
  const [specialistName, setSpecialistName] = useState<string>("");

  // Connection state management
  const [isConnected, setIsConnected] = useState(false);

  // Use useRef to prevent socket recreation on re-renders
  const socketRef = useRef<Socket | null>(null);
  // Removed unused reconnectTimeoutRef

  // Detect user role on mount
  useEffect(() => {
    const role = getDecryptedToken("Role");
    setUserRole(role);
  }, []);

  // Fetch doctor and specialist names
  useEffect(() => {
    const fetchUserNames = async () => {
      try {
        const token = getDecryptedToken("AccessToken");
        
        // Fetch current user's profile (could be doctor or specialist)
        try {
          const profileResponse = await fetch(`${API_ROUTES.PROFILE.GET_PROFILE}`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });
          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            if (profileData.success && profileData.data) {
              const firstName = profileData.data.first_name || "";
              const lastName = profileData.data.last_name || "";
              const role = getDecryptedToken("Role");
              
              if (role === "doctor") {
                setDoctorName(`Dr. ${firstName} ${lastName}`.trim());
              } else if (role === "specialist") {
                setSpecialistName(`${firstName} ${lastName}`.trim());
              }
            }
          }
        } catch (error) {
          console.error("Error fetching user profile:", error);
        }

        // For now, set default names if we couldn't fetch them
        if (!doctorName && userRole !== "doctor") {
          setDoctorName("Doctor");
        }
        if (!specialistName && userRole !== "specialist") {
          setSpecialistName("Specialist");
        }
      } catch (error) {
        console.error("Error fetching user names:", error);
      }
    };

    fetchUserNames();
  }, [doctorsId, receiverId, userRole, doctorName, specialistName]);

  // Fetch specialist (receiver) id when patientId changes
  useEffect(() => {
    if (!patientId) return;

    const role = getDecryptedToken("Role");

    // If user is specialist, receiverId should be doctorsId
    if (role === "specialist") {
      console.log(
        "DEBUG: User is specialist, setting receiverId to doctorsId:",
        doctorsId
      );
      setReceiverId(doctorsId);
      setReceiverLoading(false);
      return;
    }

    // If user is doctor, fetch specialist ID
    setReceiverLoading(true);
    const encryptedToken = getToken("AccessToken");
    const token = encryptedToken ? decryptToken(encryptedToken) : null;
    console.log("DEBUG patientId:", patientId);
    console.log("DEBUG token:", token);
    fetch(`${API_ROUTES.PATIENT.GET_SPECIALIST}/${patientId}/specialist`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        console.log("DEBUG specialist API response:", data);
        if (data.success && data.data?.id) {
          setReceiverId(data.data.id);
        } else {
          setReceiverId(null);
        }
      })
      .catch((err) => {
        console.log("DEBUG specialist API error:", err);
        setReceiverId(null);
      })
      .finally(() => setReceiverLoading(false));
  }, [patientId, doctorsId]);

  // Connect socket on mount and set up listeners
  useEffect(() => {
    // Ensure SOCKET_URL is defined before attempting to connect
    if (!SOCKET_URL) {
      console.error(
        "SOCKET_URL is not defined. Cannot connect to socket server."
      );
      return;
    }

    // Disconnect existing socket if any
    if (socketRef.current) {
      socketRef.current.removeAllListeners();
      socketRef.current.disconnect();
    }

    console.log("Connecting to socket server:", SOCKET_URL);

    const s = io(SOCKET_URL, {
      transports: ["websocket", "polling"], // Add polling as fallback
      timeout: 20000,
      reconnection: true,
      reconnectionDelay: 2000,
      reconnectionDelayMax: 10000,
      reconnectionAttempts: 5,
      forceNew: true, // Force new connection
    });

    socketRef.current = s;

    // Connection events
    s.on("connect", () => {
      console.log("Socket connected:", s.id);
      setIsConnected(true);

      // Always add user when connected, using userId from token
      const userId = getDecryptedToken("userid");
      if (userId) {
        console.log("🔗 Adding user to socket room:", {
          userId,
          userRole: getDecryptedToken("Role"),
          patientId: patientId,
        });
        s.emit("addUser", userId);
        // Also emit join like the HTML client does
        s.emit("join", userId);
      }
    });

    s.on("disconnect", (reason) => {
      console.log("Socket disconnected:", reason);
      setIsConnected(false);
    });

    s.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      setIsConnected(false);
    });

    s.on("reconnect", (attemptNumber) => {
      console.log("Socket reconnected after", attemptNumber, "attempts");
      setIsConnected(true);

      // Re-add user on reconnect
      const userId = getDecryptedToken("userid");
      if (userId) {
        console.log("Re-adding user to socket room:", userId);
        s.emit("addUser", userId);
        // Also emit join like the HTML client does
        s.emit("join", userId);
      }
    });

    // Event listeners
    s.on("messagesList", (data: Message[]) => {
      console.log("Received messagesList:", data);
      setMessages(data);
      setMessagesLoading(false);
    });

    s.on("newMessage", (message: Message) => {
      console.log("Received newMessage:", message);
      console.log(
        "Current patientId:",
        patientId,
        "Message patientId:",
        message.patient_id
      );
      // Only add message if it belongs to the currently viewed patient chat
      if (Number(message.patient_id) === Number(patientId)) {
        console.log("Adding message to current chat");
        setMessages((prev) => {
          // Prevent duplicates if the message was already added (e.g., by sender's local echo)
          const exists = prev.some((msg) => msg.id === message.id);
          if (exists) {
            console.log("Message already exists, skipping");
            return prev;
          }
          console.log("Adding new message to chat");
          return [...prev, message];
        });
        // Reset uploading state when message is successfully sent
        setIsUploading(false);
      } else {
        console.log("Message not for current patient, ignoring");
      }
    });

    s.on("messageDeleted", ({ messageId }) => {
      console.log("🗑️ RECEIVED messageDeleted event:", {
        messageId,
        currentUserId: getDecryptedToken("userid"),
        userRole: getDecryptedToken("Role"),
        patientId: patientId,
        messagesBeforeDelete: messages.length,
      });
      setMessages((prev) => {
        const filteredMessages = prev.filter((msg) => msg.id !== messageId);
        console.log("🗑️ Messages after deletion:", {
          messagesBefore: prev.length,
          messagesAfter: filteredMessages.length,
          deletedMessageId: messageId,
        });
        return filteredMessages;
      });
    });

    s.on("responseError", (errorMessage: string) => {
      console.log("SOCKET ERROR:", errorMessage);
      setErrorModalMessage(errorMessage);
      setErrorModalOpen(true);
      // Reset uploading state on error
      setIsUploading(false);
    });

    // Add success handler
    s.on("messageSent", (data) => {
      console.log("Message sent successfully:", data);
      // Reset uploading state on success
      setIsUploading(false);
    });

    // Add error handlers for debugging
    s.on("error", (error) => {
      console.error("Socket error:", error);
    });

    s.on("reconnect_error", (error) => {
      console.error("Reconnect error:", error);
    });

    s.on("reconnect_failed", () => {
      console.error("Reconnect failed");
    });

    return () => {
      if (s) {
        s.removeAllListeners(); // Remove all listeners before disconnect
        s.disconnect();
      }
    };
  }, []); // Empty dependency array - only run once

  // Ensure user is added to socket room when connected
  useEffect(() => {
    if (socketRef.current && isConnected) {
      const userId = getDecryptedToken("userid");
      if (userId) {
        console.log("Ensuring user is in socket room:", userId);
        socketRef.current.emit("addUser", userId);
        // Also emit join like the HTML client does
        socketRef.current.emit("join", userId);
      }
    }
  }, [isConnected]);

  // Separate useEffect for fetching messages when receiverId changes (initial load or chat switch)
  useEffect(() => {
    // Use token user ID consistently instead of prop currentUserId
    const tokenUserId = Number(getDecryptedToken("userid"));
    if (
      socketRef.current &&
      isConnected &&
      tokenUserId &&
      patientId &&
      receiverId
    ) {
      console.log("Fetching messages for receiverId:", receiverId);
      setMessagesLoading(true); // Set loading true before fetching
      socketRef.current.emit("getConversationMessages", {
        user_id: tokenUserId, // Use token user ID
        target_user_id: receiverId,
        patient_id: patientId,
      });
      // Also mark messages as read when a conversation is opened
      socketRef.current.emit("markMessagesAsRead", {
        user_id: tokenUserId, // Use token user ID
        sender_id: receiverId, // The receiver is the sender of messages you're now reading
        patient_id: patientId,
      });
    }
  }, [isConnected, patientId, receiverId]); // Remove currentUserId from dependencies

  // Scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Update pendingMessage when messageInput or file changes
  useEffect(() => {
    if (!messageInput && !file) {
      setPendingMessage(null);
      return;
    }
    setPendingMessage({ text: messageInput, file, filePreview });
  }, [messageInput, file, filePreview]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;  
    console.log("===============>",file)
    if (file.size > MAX_FILE_SIZE) {
      setErrorModalMessage(
        "File size exceeds the 50 MB limit. Please select a smaller file."
      );
      setErrorModalOpen(true);
      e.target.value = "";
      setFile(null);
      setFilePreview(null);
      return;
    }
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    let isAllowed = ALLOWED_FILE_TYPES.includes(file.type);
    if (!isAllowed && fileExtension === "zip") isAllowed = true;
    if (!isAllowed) {
      setErrorModalMessage(
        "File type not allowed. Please select a ZIP, PDF, or image file."
      );
      setErrorModalOpen(true);
      e.target.value = "";
      setFile(null);
      setFilePreview(null);
      return;
    }
    setFile(file);
    // Preview (no modal, just set preview state)
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        setFilePreview(
          <Image
            src={ev.target?.result as string}
            alt="preview"
            width={48}
            height={48}
            unoptimized
            className="rounded object-cover border border-gray-200"
          />
        );
      };
      reader.readAsDataURL(file);
    } else if (file.type === "application/pdf") {
      setFilePreview(<div className="text-2xl">📄</div>);
    } else if (
      [
        "application/zip",
        "application/x-zip-compressed",
        "application/x-zip",
      ].includes(file.type) ||
      fileExtension === "zip"
    ) {
      setFilePreview(<div className="text-2xl">🗜️</div>);
    } else {
      setFilePreview(null);
    }
    
    // Clear the input value so the same file can be selected again later
    e.target.value = "";
  };

  const clearFile = () => {
    setFile(null);
    setFilePreview(null);
    setPendingMessage((prev) =>
      prev ? { ...prev, file: null, filePreview: null } : null
    );
    // Clear the file input value so same file can be selected again
    const fileInput = document.getElementById("fileInput") as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const toBase64 = (file: File) =>
    new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

  const sendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!messageInput && !file) return;
    if (!currentUserId || !receiverId || !patientId) return;
    if (!isConnected || !socketRef.current) {
      setErrorModalMessage(
        "Not connected to server. Please wait for connection."
      );
      setErrorModalOpen(true);
      return;
    }

    // Set uploading state to true when starting
    setIsUploading(true);

    let fileBase64 = null,
      fileName = null,
      fileType = null,
      fileSize = null;
    if (file) {
      console.log("===============>",file)
      if (file.size > MAX_FILE_SIZE) {
        console.log("===============>",file)
        setErrorModalMessage(
          "File size exceeds the 50 MB limit. Please select a smaller file."
        );
        setErrorModalOpen(true);
        clearFile();
        setIsUploading(false); // Reset uploading state on error
        return;
      }
      const extension = file.name.split(".").pop()?.toLowerCase();
      let processedFileType = file.type;
      if (
        extension === "zip" &&
        (file.type === "application/octet-stream" ||
          ![
            "application/zip",
            "application/x-zip-compressed",
            "application/x-zip",
          ].includes(file.type))
      ) {
        processedFileType = "application/zip";
      }
      try {
        fileBase64 = await toBase64(file);
        fileName = file.name;
        fileType = processedFileType;
        fileSize = file.size;
      } catch {
        setErrorModalMessage("Failed to process the file. Please try again.");
        setErrorModalOpen(true);
        clearFile();
        setIsUploading(false); // Reset uploading state on error
        return;
      }
    }

    const targetReceiverId = userRole === "specialist" ? doctorsId : receiverId;

    if (!targetReceiverId) {
      console.error("Cannot send message: Target receiver ID is null.");
      setErrorModalMessage("Cannot send message: Receiver not identified.");
      setErrorModalOpen(true);
      setIsUploading(false); // Reset uploading state on error
      return;
    }

    console.log("SENDING MESSAGE:", {
      sender_id: Number(userId),
      receiver_id: targetReceiverId,
      patient_id: patientId,
      message: messageInput,
      file_base64: fileBase64,
      file_name: fileName,
      file_type: fileType,
      file_size: fileSize,
    });

    socketRef.current.emit("sendMessage", {
      sender_id: Number(userId),
      receiver_id: targetReceiverId,
      patient_id: patientId,
      message: messageInput,
      file_base64: fileBase64,
      file_name: fileName,
      file_type: fileType,
      file_size: fileSize,
    });

    setMessageInput("");
    clearFile();
    setPendingMessage(null);
    // Note: isUploading will be set to false when we receive the response
  };

  const deleteMessage = (messageId: number) => {
    // Use token user ID consistently
    const tokenUserId = Number(getDecryptedToken("userid"));
    const userRole = getDecryptedToken("Role");

    // Use same targetReceiverId logic as sendMessage
    const targetReceiverId = userRole === "specialist" ? doctorsId : receiverId;

    if (!tokenUserId || !targetReceiverId || !patientId) {
      console.error("Cannot delete message: Missing required data:", {
        tokenUserId,
        targetReceiverId,
        patientId,
        userRole,
      });
      return;
    }

    console.log("🗑️ SENDING deleteMessage:", {
      messageId,
      sender_id: tokenUserId,
      receiver_id: targetReceiverId,
      patient_id: patientId,
      userRole: userRole,
    });

    socketRef.current?.emit("deleteMessage", {
      messageId,
      sender_id: tokenUserId, // Use token user ID
      receiver_id: targetReceiverId, // Fixed: Use targetReceiverId instead of receiverId
      patient_id: patientId,
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDeleteClick = (messageId: number) => {
    setMessageToDelete(messageId);
    setDeleteModalOpen(true);
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setMessageToDelete(null);
  };

  const handleConfirmDelete = () => {
    if (messageToDelete !== null) {
      deleteMessage(messageToDelete);
    }
    setDeleteModalOpen(false);
    setMessageToDelete(null);
  };

  const toggleExpandMessage = (id: number) => {
    setExpandedMessages((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  // Helper to determine sender type for each message
  const doctorId = doctorsId;

  // Fix specialist ID logic based on user role
  const specialistId =
    userRole === "specialist"
      ? Number(getDecryptedToken("userid")) // If current user is specialist, use their ID
      : receiverId; // If current user is doctor, receiverId is the specialist ID

  // Sender type logic with names
  const getSenderType = (msg: Message) => {
    console.log("🔍 getSenderType DEBUG:", {
      messageId: msg.id,
      msgSenderId: msg.sender_id,
      doctorId: doctorId,
      specialistId: specialistId,
      userRole: userRole,
      receiverId: receiverId,
    });

    if (msg.sender_id === doctorId) return "doctor";
    if (msg.sender_id === specialistId) return "specialist";
    return "unknown";
  };

  // Get sender information including name
  const getSenderInfo = (msg: Message) => {
     if (msg.sender_name) {
    const senderType = getSenderType(msg);
    return {
      type: senderType,
      name: msg.sender_name,
      avatar: senderType === "doctor" ? doctorAvatar : specialistAvatar
    };
  }
    const senderType = getSenderType(msg);
    
    if (senderType === "doctor") {
      return {
        type: "doctor",
        name: doctorName || "Doctor",
        avatar: doctorAvatar
      };
    }
    if (senderType === "specialist") {
      return {
        type: "specialist", 
        name: specialistName || "Specialist",
        avatar: specialistAvatar
      };
    }
    return {
      type: "unknown",
      name: "Unknown User",
      avatar: doctorAvatar
    };
  };
  // Avatars and bubble colors
  const doctorAvatar = (
    <div className="bg-[#FFE5D4] text-[#EB6309] rounded-full w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center text-lg sm:text-2xl shadow-md border-2 border-white">
      <FaUserMd />
    </div>
  );
  const specialistAvatar = (
    <div className="bg-[#EB6309] text-[#FFE5D4] rounded-full w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center text-lg sm:text-2xl shadow-md border-2 border-white">
      <FaUserNurse />
    </div>
  );

  return (
    <div className="p-2 sm:p-4 flex flex-col flex-grow relative ">
      {/* Overlay when no specialist is found */}
      {!receiverId && !receiverLoading && (
        <div className="absolute inset-0 bg-white/80 z-50 flex items-center justify-center pointer-events-auto">
          <div className="text-red-500 font-semibold text-base sm:text-lg text-center px-2">
            No specialist found for this patient.
            <br />
            Conversation is disabled.
          </div>
        </div>
      )}
      <div
        className={`flex flex-col flex-grow ${
          !receiverId && !receiverLoading
            ? "pointer-events-none opacity-60"
            : ""
        }`}
      >
        <div className="mb-2 sm:mb-4 flex justify-between items-center ">
          <h3 className="font-semibold text-gray-800 text-base sm:text-lg">
            Doctor & Specialist Chat
          </h3>
        </div>

        {/* {receiverLoading && (
          <div className="flex items-center gap-2 text-orange-500 mb-2">
            <ImSpinner2 className="animate-spin" /> Loading specialist info...
          </div>
        )} */}
        {!receiverLoading && !receiverId && (
          <div className="text-red-500 mb-2">
            No specialist found for this patient.
          </div>
        )}
        <div className="flex-grow space-y-2 h-24 overflow-y-auto px-1 sm:px-2 pt-4 bg-gradient-to-br from-[#f9fafb] to-[#f3f4f6] border border-gray-200 rounded-2xl shadow-xl mb-2 transition-all duration-200">
          {messagesLoading ? (
            <div className="flex items-center justify-center h-full text-orange-500">
              <ImSpinner2 className="animate-spin w-8 h-8 mr-2" /> Loading
              messages...
            </div>
          ) : (
            <>
              {!messagesLoading && messages.length === 0 && !pendingMessage && (
                <div className="text-center text-gray-400 pt-10 sm:pt-20 text-xs sm:text-base">
                  No messages yet. Start the conversation!
                </div>
              )}
              {messages.map((convo, idx: number) => {
                const senderInfo = getSenderInfo(convo);

                // Get current user ID consistently
                const currentUserId = Number(getDecryptedToken("userid"));

                // Ensure both values are numbers for proper comparison
                const messageSenderId = Number(convo.sender_id);
                const isOwnMessage = messageSenderId === currentUserId;

                // Enhanced debug logging for message ownership
                console.log("DEBUG Message ownership:", {
                  messageId: convo.id,
                  message: convo.message?.substring(0, 20) + "...",
                  messageSenderId: messageSenderId,
                  currentUserId: currentUserId,
                  propCurrentUserId: currentUserId, // This is the prop (always doctor_id)
                  tokenUserId: Number(getDecryptedToken("userid")), // This is from token
                  userId: Number(userId),
                  userRole: userRole,
                  isOwnMessage: isOwnMessage,
                  senderType: senderInfo.type,
                  senderName: senderInfo.name,
                  doctorsId: doctorsId,
                  receiverId: receiverId,
                  comparison: `${messageSenderId} === ${currentUserId} = ${
                    messageSenderId === currentUserId
                  }`,
                });

                const showOnRight = isOwnMessage;

                const avatar = senderInfo.avatar;
                const bubbleBg =
                  senderInfo.type === "doctor" ? "bg-white" : "bg-[#FFE5D4]";
                const alignClass = showOnRight
                  ? "justify-end items-end"
                  : "justify-start items-start";
                const bubbleAlign = showOnRight ? "items-end" : "items-start";
                const bubbleRound = showOnRight
                  ? "rounded-2xl rounded-br-md"
                  : "rounded-2xl rounded-bl-md";
                
                // Use actual name instead of generic label
                const senderLabel = senderInfo.name;

                // Message truncation logic
                const messageText = convo.message || "";
                const isLongMessage = messageText.length > 200;
                const isExpanded = expandedMessages[convo.id];
                const displayedMessage =
                  isLongMessage && !isExpanded
                    ? messageText.slice(0, 200) + "..."
                    : messageText;

                return (
                  <div
                    key={`${convo.id}-${idx}`}
                    className={`flex ${alignClass} fade-in-message`}
                    style={{ marginBottom: 12 }}
                  >
                    {/* Avatar left side for receiver messages */}
                    {!showOnRight && (
                      <div className="mr-1 sm:mr-2 z-10">{avatar}</div>
                    )}

                    <div
                      className={`relative flex flex-col ${bubbleAlign} group w-full`}
                    >
                      <div
                        className={`relative w-fit max-w-[85vw] sm:max-w-[350px] md:max-w-[420px] min-w-[60px] sm:min-w-[80px] ${bubbleBg} ${bubbleRound}
                          px-3 py-2 sm:px-5 sm:py-3 shadow-lg transition-all duration-200
                          hover:scale-[1.025] hover:shadow-xl
                          border border-gray-200
                        `}
                        style={{ minWidth: 60 }}
                      >
                        {/* WhatsApp-style tail */}
                        {showOnRight ? (
                          <span
                            className="hidden sm:block absolute -bottom-2 right-3 w-0 h-0 border-t-8 border-t-current border-l-8 border-l-transparent border-r-0 border-b-0"
                            style={{
                              borderTopColor:
                                senderInfo.type === "doctor" ? "#dcfce7" : "#dbeafe",
                            }}
                          ></span>
                        ) : (
                          <span
                            className="hidden sm:block absolute -bottom-2 left-3 w-0 h-0 border-t-8 border-t-current border-r-8 border-r-transparent border-l-0 border-b-0"
                            style={{
                              borderTopColor:
                                senderInfo.type === "doctor" ? "#dcfce7" : "#dbeafe",
                            }}
                          ></span>
                        )}

                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs font-semibold opacity-80">
                            {senderLabel}
                          </span>
                        </div>

                        <div
                          className="text-xs sm:text-sm whitespace-pre-line"
                          style={{
                            wordBreak: "break-all",
                            overflowWrap: "anywhere",
                          }}
                        >
                          {displayedMessage}
                          {isLongMessage && (
                            <button
                              className="ml-2 font-bold underline text-xs focus:outline-none"
                              style={{
                                textShadow: "0 1px 2px rgba(0,0,0,0.25)",
                              }}
                              onClick={() => toggleExpandMessage(convo.id)}
                            >
                              {isExpanded ? "See less" : "See more"}
                            </button>
                          )}
                        </div>

                        {convo.file_url && (
                          <div className={`p-2 rounded mt-2 ${bubbleBg}`}>
                            {convo.file_type?.startsWith("image/") ? (
                              // Image preview
                              <Image
                                src={getFileUrl(convo.file_url)}
                                alt={
                                  typeof convo.file_name === "string"
                                    ? convo.file_name
                                    : ""
                                }
                                width={128}
                                height={160}
                                className="mt-2 rounded w-24 h-28 sm:w-32 sm:h-40 object-cover cursor-pointer border border-gray-200"
                                onClick={() => {
                                  setModalImageUrl(getFileUrl(convo.file_url));
                                  setImageModalOpen(true);
                                }}
                              />
                            ) : (
                              // Non-image file display
                              <a
                                href={getFileUrl(convo.file_url)}
                                className="flex items-center gap-2 p-2 bg-[#ffe5d4] border border-[#ffe5d4] rounded-lg text-black-700 hover:bg-blue-100  hover:border-blue-100 hover:text-blue-900 transition cursor-pointer shadow-sm"
                                target="_blank"
                                rel="noopener noreferrer"
                                download={
                                  typeof convo.file_name === "string"
                                    ? convo.file_name
                                    : undefined
                                }
                                title={`Download ${convo.file_name || "file"}`}
                              >
                                {/* File Icon */}
                                {convo.file_type === "application/pdf" ? (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="w-6 h-6 flex-shrink-0"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                  >
                                    <path d="M19 12v7a2 2 0 01-2 2H7a2 2 0 01-2-2v-7m12 0l-3-3m0 0l-3 3m3-3V4" />
                                  </svg>
                                ) : convo.file_type === "application/zip" ||
                                  convo.file_type ===
                                    "application/x-zip-compressed" ||
                                  convo.file_name
                                    ?.toLowerCase()
                                    .endsWith(".zip") ? (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="w-6 h-6 flex-shrink-0"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                  >
                                    <path
                                      d="M8 7h8M8 11h8M8 15h4"
                                      stroke="#B45309"
                                      strokeWidth="1.5"
                                      strokeLinecap="round"
                                    />
                                    <rect
                                      x="3"
                                      y="3"
                                      width="18"
                                      height="18"
                                      rx="3"
                                      fill="#FBBF24"
                                      stroke="#B45309"
                                      strokeWidth="1.5"
                                    />
                                    <rect
                                      x="10.5"
                                      y="16"
                                      width="3"
                                      height="3"
                                      rx="0.7"
                                      fill="#B45309"
                                    />
                                  </svg>
                                ) : (
                                  // Generic file icon
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="w-6 h-6 flex-shrink-0"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                  >
                                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
                                    <path
                                      fillRule="evenodd"
                                      d="M14 2v6h6l-6-6z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                )}
                                {/* File Name and Size */}
                                <div className="flex flex-col min-w-0">
                                  <span className="font-semibold text-sm truncate w-full break-words overflow-anywhere">
                                    {convo.file_name}
                                  </span>
                                  <span className="text-xs text-blue-600">
                                    {formatFileSize(convo.file_size)}
                                  </span>
                                </div>
                              </a>
                            )}
                          </div>
                        )}
                        {/* Delete button - only for own messages */}
                        {isOwnMessage && (
                          <button
                            className={`absolute top-1/2 ${
                              showOnRight ? "-left-8" : "-right-8"
                            } -translate-y-1/2 text-red-500 hover:text-red-700 bg-white/80 rounded-full p-1 shadow transition opacity-0 group-hover:opacity-100 focus:opacity-100 cursor-pointer border border-red-100`}
                            onClick={() => handleDeleteClick(convo.id)}
                            title="Delete message"
                            style={{ zIndex: 2 }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-5 h-5"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h2a2 2 0 012 2v2"
                              />
                            </svg>
                          </button>
                        )}
                      </div>

                      {/* Timestamp - align based on message side */}
                      <div
                        className={`text-[10px] text-gray-400 select-none mt-1 ${
                          showOnRight ? "text-right pr-2" : "text-left pl-2"
                        }`}
                        style={{ minWidth: 60 }}
                      >
                        {new Date(convo.created_at).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </div>
                    </div>

                    {/* Avatar right side for sender messages */}
                    {showOnRight && (
                      <div className="ml-1 sm:ml-2">{avatar}</div>
                    )}
                  </div>
                );
              })}

              {/* Uploading indicator */}
              {isUploading && (
                <div className="flex justify-end items-end mb-4">
                  <div className="relative flex flex-col items-end">
                    <div className="relative w-fit max-w-[85vw] sm:max-w-[350px] md:max-w-[420px] min-w-[60px] sm:min-w-[80px] bg-gray-100 rounded-2xl rounded-br-md px-3 py-2 sm:px-5 sm:py-3 shadow-lg border border-gray-200">
                      <div className="flex items-center gap-2">
                        <ImSpinner2 className="animate-spin w-4 h-4 text-orange-500" />
                        <span className="text-sm text-gray-600">
                          {file ? "Uploading file..." : "Sending message..."}
                        </span>
                      </div>
                    </div>
                    <div className="text-[10px] text-gray-400 select-none mt-1 text-right pr-2">
                      Uploading...
                    </div>
                  </div>
                  <div className="ml-1 sm:ml-2">
                    <div className="bg-[#FFE5D4] text-[#EB6309] rounded-full w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center text-lg sm:text-2xl shadow-md border-2 border-white">
                      <FaUserMd />
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </>
          )}
        </div>
        <form
          onSubmit={sendMessage}
          className="mt-2 flex gap-1 sm:gap-2 items-center bg-[#f8fafc] border border-gray-200 rounded-full px-2 sm:px-3 py-1.5 sm:py-2 shadow-md  sticky bottom-0 z-20"
        >
          <div className="flex items-center flex-grow relative gap-2">
            {/* Inline file preview inside input area */}
            {file && filePreview && (
              <div className="flex items-center gap-1 relative flex-shrink-0 max-w-[140px]">
                <div className="relative">
                  {filePreview}
                  <button
                    type="button"
                    className="absolute -top-2 -right-2 bg-white border border-gray-300 rounded-full w-5 h-5 flex items-center justify-center text-xs text-gray-600 hover:text-red-500 shadow"
                    onClick={clearFile}
                    title="Remove file"
                    tabIndex={-1}
                  >
                    &times;
                  </button>
                </div>
                <span className="text-xs truncate max-w-[80px] text-gray-700">
                  {file.name}
                </span>
              </div>
            )}
            <input
              type="text"
              placeholder={
                isUploading ? "Uploading..." : "Type your message..."
              }
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              className="flex-grow p-2 border-none outline-none bg-transparent text-xs sm:text-sm"
              disabled={!receiverId || receiverLoading || isUploading}
              style={{ minWidth: 0 }}
            />
          </div>
          <input
            type="file"
            id="fileInput"
            style={{ display: "none" }}
            onChange={handleFileChange}
            placeholder="Attach a file"
          />
          <button
            type="button"
            className="p-2 bg-gray-200 hover:bg-[#ffe5d4] rounded-full transition focus:outline-none focus:ring-2 focus:ring-[#ffe5d4]"
            onClick={() => document.getElementById("fileInput")?.click()}
            title="Attach file"
            disabled={!receiverId || receiverLoading || isUploading}
          >
            <FiPaperclip className="w-5 h-5 cursor-pointer text-[#eb6309]" />
          </button>
          <button
            type="submit"
            className={`p-2 ${
              isUploading ? "bg-gray-400" : "bg-primary hover:bg-orange-700"
            } text-white rounded-full shadow transition focus:outline-none focus:ring-2 focus:ring-orange-300`}
            title={isUploading ? "Uploading..." : "Send message"}
            disabled={
              !receiverId || receiverLoading || !isConnected || isUploading
            }
          >
            {isUploading ? (
              <ImSpinner2 className="w-5 h-5 animate-spin" />
            ) : (
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M14 5l7 7m0 0l-7 7m7-7H3"
                ></path>
              </svg>
            )}
          </button>
        </form>
        <DeleteConfirmationModal
          confirmModalOpen={deleteModalOpen}
          addressToDelete={
            messageToDelete !== null
              ? { type: "billing", id: String(messageToDelete) }
              : null
          }
          cancelDeleteAddress={handleCancelDelete}
          confirmDeleteAddress={handleConfirmDelete}
          customMessage="Are you sure you want to delete this message?"
        />
        {errorModalOpen && (
          <div className="fixed inset-0 flex items-center justify-center bg-black/30 z-50">
            <div className="bg-white rounded-lg shadow-lg p-6 w-[90vw] max-w-[350px] flex flex-col gap-4">
              <h2 className="text-lg font-bold text-red-600">File Error</h2>
              <p className="text-gray-700">{errorModalMessage}</p>
              <button
                className="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-orange-700 transition"
                onClick={() => setErrorModalOpen(false)}
              >
                Close
              </button>
            </div>
          </div>
        )}
        {/* Image Modal Popup */}
        {imageModalOpen && modalImageUrl && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
            onClick={() => setImageModalOpen(false)}
          >
            <div
              className="bg-white rounded-lg shadow-lg p-2 max-w-[95vw] max-h-[90vh] flex flex-col items-center relative"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                className="absolute top-2 right-2 text-gray-600 hover:text-red-500 text-2xl font-bold"
                onClick={() => setImageModalOpen(false)}
              >
                &times;
              </button>
              <Image
                src={modalImageUrl}
                alt="Preview"
                width={400}
                height={400}
                className="rounded max-h-[80vh] max-w-full object-contain"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationsTab;
