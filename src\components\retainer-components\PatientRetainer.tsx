"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useEffect, useRef, useState } from "react";
import { z } from "zod";
import FormWrapper from "../reuseable/FormWrapper";
import FormHeading from "../reuseable/FormHeading";
import CustomInput from "../reuseable/CustomInput";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import CustomDateInput from "../reuseable/CustomDateInput";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { Address, PatientFileData } from "@/types/types";
import { toast } from "react-toastify";
import { setEncryptedToken } from "@/app/lib/auth";

const patientDataSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  gender: z.enum(["male", "female"], {
    errorMap: () => ({ message: "Gender is required" }),
  }),
  birthdate: z.coerce.date({
    errorMap: () => ({
      message: "Birthdate is required and must be a valid date",
    }),
  }),
  country: z.enum(["saudi-arabia", "bahrain"], {
    errorMap: () => ({ message: "Country is required" }),
  }),
  shipAddress: z.string().min(1, "Shipping to address is required"),
  billAddress: z.string().min(1, "Billing address is required"),
});

type UserFormData = z.infer<typeof patientDataSchema>;
const countryOptions = [
  { value: "saudi-arabia", label: "Saudi Arabia" },
  { value: "bahrain", label: "Bahrain" },
];

const PatientRetainer: React.FC<{
  data: Address[];
  patientData: PatientFileData | null;
}> = ({ data, patientData }) => {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<UserFormData>({
    resolver: zodResolver(patientDataSchema),
  });

  const [gender, setGender] = useState<string>("");
  const [shipAddressid, setShipAddressid] = useState<string>("");
  const [billAddress, setBillAddress] = useState<string>("");
  const [countryOpen, setCountryOpen] = useState(false);
  const [countryLabel, setCountryLabel] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  // Prefill form if patientData is available
  useEffect(() => {
    if (patientData) {
      setValue("firstName", patientData.first_name || "");
      setValue("lastName", patientData.last_name || "");
      setValue(
        "gender",
        (patientData.gender === "male" || patientData.gender === "female"
          ? patientData.gender
          : "") as "male" | "female",
      );
      if (patientData.dob) {
        setValue("birthdate", new Date(patientData.dob));
      }
      setValue(
        "country",
        (patientData.country || countryLabel || "") as
        | "saudi-arabia"
        | "bahrain",
      );
      setGender(patientData.gender || "");
    }
  }, [patientData, setValue]);

  useEffect(() => {
    if (data && data.length > 0) {
      // Ship To default
      const firstShip = data.find((address) => address.address_type === "ship_to");
      if (firstShip) {
        setShipAddressid(String(firstShip.id));
        setValue("shipAddress", String(firstShip.id));
      }
      // Bill To default
      const firstBill = data.find((address) => address.address_type === "bill_to");
      if (firstBill) {
        setBillAddress(String(firstBill.id));
        setValue("billAddress", String(firstBill.id));
      }
    }
  }, [data, setValue]);

  // Click outside handler
  useEffect(() => {
    if (!countryOpen) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        countryDropdownRef.current &&
        !countryDropdownRef.current.contains(event.target as Node)
      ) {
        setCountryOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [countryOpen]);

  const onSubmit = async (formData: UserFormData): Promise<void> => {
    const patientData = {
      step: "step1",
      first_name: formData.firstName,
      last_name: formData.lastName,
      dob: formData.birthdate.toISOString().split("T")[0], // Format: YYYY-MM-DD
      gender: formData.gender,
      ship_to_office: shipAddressid,
      bill_to_office: billAddress,
      country: formData.country,
    };
    setLoading(true);
    const patient = JSON.stringify(patientData);
    setEncryptedToken("retainerData", patient, true);
    toast.success("Patient data saved successfully");
    setLoading(false);
    router.push("/treatment-retainer-option");
  };

  useEffect(() => {
    localStorage.removeItem("treatmentOption");
  }, []);

  return (
    <>
      <FormWrapper
        onSubmit={handleSubmit(onSubmit)}
        onBack={() => router.back()}
        showCancelButton={false}
      >
        <div className="col-span-1 flex flex-col justify-between gap-6">
          <div>
            <FormHeading text="Patient Name*" />
            <div className="flex flex-col gap-2">
              <CustomInput
                type="text"
                placeholder="First Name"
                className="!py-3.5"
                register={register("firstName")}
                error={errors.firstName?.message}
              />
              <CustomInput
                type="text"
                placeholder="Last Name"
                className="!py-3.5"
                register={register("lastName")}
                error={errors.lastName?.message}
              />
            </div>
          </div>

          <div>
            <FormHeading text="Country*" />
            <div className="relative" ref={countryDropdownRef}>
              <button
                type="button"
                className={`w-full px-4 py-3 border border-gray-300 rounded-full text-left bg-white ${errors.country ? "border-red-500" : ""} ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={() => !loading && setCountryOpen((open) => !open)}
                tabIndex={0}
                disabled={loading}
              >
                {countryOptions.find((opt) => opt.value === watch("country"))
                  ?.label || "Select Country"}
              </button>
              {countryOpen && !loading && (
                <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-1 shadow">
                  {countryOptions.map((option) => (
                    <li
                      key={option.value}
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => {
                        setCountryOpen(false);
                        setCountryLabel(option.label);
                        setValue(
                          "country",
                          option.value as "saudi-arabia" | "bahrain",
                          { shouldValidate: true },
                        );
                      }}
                    >
                      {option.label}
                    </li>
                  ))}
                </ul>
              )}
              {errors.country && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.country.message}
                </p>
              )}
            </div>
          </div>

          <div>
            <FormHeading text="Patient Gender*" />
            <div className="flex items-center gap-4">
              <RoundRadioButton
                id="gender-male"
                label="Male"
                value="male"
                name="gender"
                onClick={(e: React.MouseEvent<HTMLInputElement>) =>
                  setGender((e.target as HTMLInputElement).value as "male")
                }
                register={register}
                defaultChecked={gender === "male"}
              />
              <RoundRadioButton
                id="gender-female"
                label="Female"
                value="female"
                name="gender"
                register={register}
                onClick={(e: React.MouseEvent<HTMLInputElement>) =>
                  setGender((e.target as HTMLInputElement).value as "female")
                }
                defaultChecked={gender === "female"}
              />
            </div>
            {errors.gender && (
              <p className="text-red-500 text-sm mt-1">
                {errors.gender.message}
              </p>
            )}
          </div>

          <div>
            <FormHeading text="Date of Birth*" />
            <div className="">
              <CustomDateInput
                value={watch("birthdate")}
                register={register("birthdate")}
                id="birthDate"
                name="birthdate"
                className=""
                minDate={new Date(1900, 0, 1)} // January 1, 1900
                maxDate={new Date()} // Today
                disabled={loading}
              />
            </div>
            {errors?.birthdate?.message && (
              <p className="text-red-500 text-sm mt-1">
                {errors?.birthdate?.message}
              </p>
            )}
          </div>
        </div>

        <div className="col-span-1">
          <div className="flex flex-col gap-4">
            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <FormHeading classes={'!mb-0'} text="Ship To Office*" />
                </div>
                <div>
                  <button
                    type="button"
                    onClick={() => router.push('/account/dr-profile')}
                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                  >
                    Add New
                  </button>
                </div>
              </div>
              <div className="flex flex-col gap-5">
                {data?.filter((address) => address.address_type === "ship_to").length === 0 ? (
                  <div className="text-gray-400 text-sm">No result found</div>
                ) : (
                  data
                    ?.filter((address) => address.address_type === "ship_to")
                    .map((address) => (
                      <RoundRadioButton
                        key={address?.id}
                        id={`ship-${address?.id}`}
                        label={`${address?.clinic_name} (#${address?.postal_code}), ${address?.street_address}, ${address?.city}`}
                        value={String(address?.id)}
                        name="shipAddress"
                        defaultChecked={shipAddressid === String(address?.id)}
                        onClick={() => setShipAddressid(String(address?.id))}
                        register={register}
                      />
                    ))
                )}
              </div>
              {errors.shipAddress && (
                <p className="text-red-500 text-sm mt-1">
                  {errors?.shipAddress.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <FormHeading classes={'!mb-0'} text="Bill To Office*" />
                </div>
                <div>
                  <button
                    type="button"
                    onClick={() => router.push('/account/dr-profile')}
                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                  >
                    Add New
                  </button>
                </div>
              </div>
              <div className="flex flex-col gap-5">
                {data?.filter((address) => address.address_type === "bill_to").length === 0 ? (
                  <div className="text-gray-400 text-sm">No result found</div>
                ) : (
                  data
                    ?.filter((address) => address.address_type === "bill_to")
                    .map((address) => (
                      <RoundRadioButton
                        key={address?.id}
                        id={`bill-${address?.id}`}
                        label={`${address?.clinic_name} (#${address?.postal_code}), ${address?.street_address}, ${address?.city}`}
                        value={String(address?.id)}
                        name="billAddress"
                        defaultChecked={billAddress === String(address?.id)}
                        onClick={() => setBillAddress(String(address?.id))}
                        register={register}
                      />
                    ))
                )}
              </div>
              {errors.billAddress && (
                <p className="text-red-500 text-sm mt-1">
                  {errors?.billAddress.message}
                </p>
              )}
            </div>
          </div>
        </div>
      </FormWrapper>
    </>
  );
};

export default PatientRetainer;
