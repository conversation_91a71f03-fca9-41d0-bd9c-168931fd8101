"use client";
import { useState } from "react";
import upper from "../../../public/svgs/upperImpression.svg";
import lower from "../../../public/svgs/lowerImpression.svg";
import Image from "next/image";
import { TabName } from "./RefinementModel";

interface props {
  setTab: (val: TabName, force: boolean) => void;
  onClose: () => void;
  setScanData: React.Dispatch<
    React.SetStateAction<{
      upperSTL?: File;
      upperSTLName?: string;
      lowerSTL?: File;
      lowerSTLName?: string;
    }>
  >;
}

const Scan: React.FC<props> = ({ setTab, onClose, setScanData }) => {
  const [upload1, setUpload1] = useState<File>();
  const [upload2, setUpload2] = useState<File>();
  const [error1, setError1] = useState("");

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    let isError = false;
    if (!upload1 || !upload2) {
      setError1("Both arches are required");
      isError = true;
    }
    if (isError) {
      return;
    }

    // Update parent state
    setScanData({
      upperSTL: upload1,
      upperSTLName: upload1?.name,
      lowerSTL: upload2,
      lowerSTLName: upload2?.name,
    });

    setTab("photos", true);
  };

  const handleFile1Change = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const single = e.target.files[0];
      setUpload1(single);
    }
  };

  const handleFile2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const single = e.target.files[0];
      setUpload2(single);
    }
  };

  return (
    <div className="flex-grow flex flex-col">
      <form
        className="flex-grow flex flex-col justify-between"
        onSubmit={onSubmit}
      >
        <h3 className="my-5 text-dark">
          Please upload your 3D scans (only .STL, .PLY and .DCM files are
          accepted)
        </h3>

        <div className="mb-4 grid grid-cols-4 gap-3 flex-grow">
          <label
            htmlFor="stl-input-1"
            className="flex flex-col items-center justify-between p-4 bg-white shadow-md cursor-pointer transition rounded-lg col-span-1"
          >
            <h4 className="text-gray font-semibold ">Upper Impression</h4>
            {upload1 && <p className="text-sm text-gray">{upload1.name}</p>}
            <div className="relative w-32 h-32">
              <Image
                className="object-cover"
                fill
                src={upper}
                alt="Upper impression"
              />
            </div>
            <input
              id="stl-input-1"
              type="file"
              accept=".zip"
              onChange={handleFile1Change}
              className="hidden"
            />
          </label>
          <label
            htmlFor="stl-input-2"
            className="flex flex-col items-center justify-between p-4 bg-white shadow-md cursor-pointer transition rounded-lg col-span-1"
          >
            <h4 className="text-gray font-semibold ">Lower Impression</h4>
            {upload2 && <p className="text-sm text-gray">{upload2.name}</p>}
            <div className="relative w-32 h-32">
              <Image
                className="object-cover"
                fill
                src={lower}
                alt="Lower impression"
              />
            </div>
            <input
              id="stl-input-2"
              type="file"
              accept=".zip"
              onChange={handleFile2Change}
              className="hidden"
            />
          </label>
        </div>

        {error1 && <p className="text-sm text-danger">{error1}</p>}

        <div className="flex items-center justify-end gap-3 my-4">
          <button
            onClick={() => onClose()}
            className="flex items-center justify-center py-2 cursor-pointer rounded-full border border-gray min-w-[120px]"
            type="button"
          >
            <span className="font-semibold text-lg text-dark">Cancel</span>
          </button>
          <button
            className="flex items-center justify-center py-1.5 cursor-pointer rounded-full bg-primary min-w-48 hover:bg-[#D45A08] transition"
            type="submit"
          >
            <span className="font-semibold text-base text-white">Next</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default Scan;
