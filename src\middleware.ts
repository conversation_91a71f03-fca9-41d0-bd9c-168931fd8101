import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { API_ROUTES } from "./utils/ApiRoutes";
import * as CryptoJS from "crypto-js";
import { getServerCookies } from "./api/getapis";

const ENCRYPTION_KEY =
  process.env.ENCRYPTION_KEY || "7d7cd92a9c";


export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Public routes that don't require authentication
  const publicRoutes = ["/login", "/forgot-password"];
  const isPublicRoute = publicRoutes.some((path) => pathname.startsWith(path));

  // Get encrypted token from cookies
  const token = await getServerCookies("AccessToken");


  // Helper: build login URL and set a ReturnTo cookie for protected routes
  const buildLoginRedirect = () => {
    const loginUrl = new URL("/login", request.url);
    // Only store intended destination when coming from a protected route
    if (!isPublicRoute) {
      // Store only the pathname to avoid exposing query params if undesired
      const intendedPath = request.nextUrl.pathname;
      const encrypted = CryptoJS.AES.encrypt(
        intendedPath,
        ENCRYPTION_KEY

      ).toString();
      const res = NextResponse.redirect(loginUrl);
      // Non-HttpOnly so client can read and clear after login
      res.cookies.set("ReturnTo", encrypted, {
        path: "/",
        sameSite: "lax",
      });
      return res;
    }
    return NextResponse.redirect(loginUrl);
  };

  // If no token and trying to access a protected route, redirect to login

  if (!token && !isPublicRoute) {
    return buildLoginRedirect();
  }

  if (token) {
    const decryptedToken = token;


    // If token is invalid or decryption fails, clear cookie and redirect
    if (!decryptedToken) {
      const response = buildLoginRedirect();
      response.cookies.delete("AccessToken");
      return response;
    }

    try {
      // Verify session with the backend
      const response = await fetch(API_ROUTES.AUTH.CHECK_SESSION, {
        headers: {
          Authorization: `Bearer ${decryptedToken}`,
        },
      });
      // console.log("🚀 ~ middleware ~ response:", response)


      // If session is valid
      if (response.ok) {
        const sessionData = await response.json();
        if (sessionData.success === true) {
          // If authenticated and on a public route, redirect to dashboard
          if (isPublicRoute) {
            return NextResponse.redirect(new URL("/dashboard", request.url));
          }
          // Otherwise, allow access to the protected route
          return NextResponse.next();
        }
      }

      // If session is invalid (e.g., 401 from API), redirect to login and clear cookie
      const responseRedirect = buildLoginRedirect();
      responseRedirect.cookies.delete("AccessToken");
      return responseRedirect;
    } catch (error) {
      console.error("Middleware session check API call failed:", error);
      // If API call fails, it's safer to redirect to login for protected routes
      if (!isPublicRoute) {
        return buildLoginRedirect();
      }
    }
  }

  // Allow access to public routes by default if no token
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     * - svgs (public svgs)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|images|svgs).*)",
  ],
};
