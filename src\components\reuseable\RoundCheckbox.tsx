import React, { useRef, useState } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import TickIcon from "./Icons/TickIcon";

interface CustomCheckboxProps {
  label?: string;
  sup?: string;
  labelClass?: string;
  register?: UseFormRegisterReturn;
  error?: string;
  className?: string;
  id: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
}

const RoundCheckbox: React.FC<CustomCheckboxProps> = ({
  label,
  register,
  error,
  className,
  id,
  value,
  onChange,
  labelClass,
  sup,
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [render, setRender] = useState<boolean>(false);

  const reRender = () => {
    setRender((prev) => !prev);
  };
  return (
    <div className="flex items-center gap-1">
      {false && render && <span></span>}
      <label htmlFor={id} className={` flex items-center gap-1 cursor-pointer`}>
        <input
          onClick={reRender}
          onChange={onChange}
          value={value}
          type="checkbox"
          id={id}
          {...register}
          className="peer hidden accent-orange-600"
          ref={(el) => {
            register?.ref?.(el);
            inputRef.current = el;
          }}
        />
        <div
          className={`w-6 h-6 appearance-none border flex justify-center items-center border-gray rounded-full peer-checked:bg-primary peer-checked:border-transparent ${className}`}
        >
          {inputRef.current?.checked && (
            <TickIcon fill="#FFFFFF" classess="w-3 h-3" />
          )}
        </div>
        {label && (
          <span className={`${labelClass} text-gray`}>
            {label}
            <sup>{sup}</sup>
          </span>
        )}
      </label>

      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default RoundCheckbox;
