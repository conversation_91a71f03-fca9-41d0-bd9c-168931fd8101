"use client";
import "@/app/globals.css";
import Header from "@/components/reuseable/Header";
import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { getDecryptedToken } from "@/app/lib/auth";

export default function DefaultLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("");

  const role = getDecryptedToken("Role");

  useEffect(() => {
    if (pathname === "/account") {
      if (role === "specialist") {
        router.replace("/account/specialist-profile");
      } else {
        router.replace("/account/summary");
      }
    }
  }, [pathname, role, router]);

  useEffect(() => {
    const pathParts = pathname.split("/");
    const lastPart = pathParts[pathParts.length - 1];
    setActiveTab(lastPart.charAt(0).toUpperCase() + lastPart.slice(1));
  }, [pathname]);

  const handleProfileRedirect = () => {
    if (role === "specialist") {
      router.push("/account/specialist-profile");
    } else {
      router.push("/account/dr-profile");
    }
  };

  // Check if current tab is a profile tab (either type)
  const isProfileActive =
    activeTab === "Dr-profile" || activeTab === "Specialist-profile";

  // console.log("User Role:", role);
  return (
    <div className="px-4 py-2">
      <Header onSearchChange={() => {}} searchValue={""} />

      {["Payments", "Dr-profile", "Specialist-profile", "Staff"].includes(
        activeTab,
      ) && (
        <div className="space-x-4 px-3">
          {/* {role !== 'specialist' && (
                        <button
                            type="button"
                            onClick={() => router.push('/account/summary')}
                            className={`p-4 rounded-full cursor-pointer ${activeTab === "Summary" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
                        >
                            Summary
                        </button>
                    )} */}
          {/* <button
                    onClick={() => router.push('/account/payments')}
                    className={`p-4 rounded-full cursor-pointer ${activeTab === "Payments" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
                >
                    Payments
                </button> */}
          <button
            onClick={handleProfileRedirect}
            className={`px-4 py-1.5 rounded-full cursor-pointer ${isProfileActive ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
          >
            Profile
          </button>

          {role !== "employee" && role !== "specialist" && (
            <button
              onClick={() => router.push("/account/staff")}
              className={`px-4 py-1.5 rounded-full cursor-pointer ${activeTab === "Staff" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
            >
              Staff
            </button>
          )}
        </div>
      )}
      <div className="p-4">{children}</div>
    </div>
  );
}
