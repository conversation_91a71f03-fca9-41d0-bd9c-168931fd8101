import type {
  AddAddressPayload,
  AlignerReplacementResponse,
  CreatePatientResponse,
  DeleteAddressPayload,
  DoctorProfileApiResponse,
  EmployeeApiResponse,
  RefinementResponse,
  RetainerRequestPayload,
  RetainerRequestResponse,
  SubmitRefinementPayload,
  UpdateDoctorProfilePayload,
  UpdatePatientStatusResponse,
  UploadCbctPayload,
  UploadCbctResponse,
} from "@/types/types"
import { API_ROUTES, API_SERVER_ROUTES } from "./ApiRoutes"
import type { ApiResponse } from "@/types/types"
import { toast } from "react-toastify"
import { getDecryptedToken } from "@/app/lib/auth"

// Define proper response types
interface LoginResponse extends ApiResponse {
  data?: {
    accessToken?: string
    user?: Record<string, unknown>
  }
}

interface ForgetPasswordResponse extends ApiResponse {
  data?: {
    message?: string
  }
}

interface ClinicalConditionsResponse extends ApiResponse {
  data?: {
    conditions?: Record<string, unknown>
  }
}

interface PatientDataResponse extends ApiResponse {
  data?: {
    records?: Record<string, unknown>
  }
}

interface CasePrescriptionResponse extends ApiResponse {
  data?: {
    prescription?: Record<string, unknown>
  }
}

export const loginUser = async (
  identifier: string,
  password: string,
  remember?: boolean,
): Promise<LoginResponse | null> => {
  try {
    const formData = new FormData()
    const isEmail = identifier.includes("@")

    formData.append(isEmail ? "email" : "username", identifier)
    formData.append("password", password)
    if (remember) {
      formData.append("remember", remember.toString())
    }

    const response = await fetch(`${API_ROUTES.AUTH.LOGIN}`, {
      method: "POST",
      body: formData,
    })

    const data = await response.json()

    // ✅ Always return parsed data even on error
    return {
      ...data,
      success: response.ok, // Mark success based on HTTP status
    }
  } catch {
    return null
  }
}

export const forgetPassword = async (
  identifier: string,
): Promise<ForgetPasswordResponse> => {
  let data: ForgetPasswordResponse; // Declare data here
  try {
    const formData = new FormData()
    const isEmail = identifier.includes("@") && identifier.includes(".")

    if (isEmail) {
      formData.append("email", identifier)
    } else {
      formData.append("username", identifier)
    }

    const response = await fetch(`${API_ROUTES.AUTH.FORGET_PASSWORD}`, {
      method: "POST",
      headers: {
        accept: "application/json",
      },
      body: formData,
    });
    data = await response.json();
    if (!response.ok) {
      return data;
    }
  } catch (error) {
    data = {
      status: 500,
      success: false,
      message:
        typeof error === "object" && error !== null && "message" in error
          ? (error as { message?: string }).message || "An unexpected error occurred."
          : "An unexpected error occurred.",
    };
    return data;
  }
  return data;
};

export const fetchPaginatedData = async <T,>(url: string, page = 1, limit = 50, token: string): Promise<T | null> => {
  try {
    if (!token) {
      return null
    }

    if (page <= 0 || limit <= 0) {
      return null
    }

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    }).toString()
    const fullUrl = `${url}?${queryParams}`

    const response = await fetch(fullUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      return null
    }

    const data: T = await response.json()
    if (!data) {
      return null
    }

    return data
  } catch {
    return null
  }
}

export const createPatient = async (
  token: string,
  patientData: {
    planid: string
    country: string
    step: string
    first_name: string
    last_name: string
    dob: string
    gender: string
    ship_to_office: string
    bill_to_office: string
  },
  id?: string,
): Promise<CreatePatientResponse | null> => {
  try {
    if (!token) {
      return null
    }

    const formData = new FormData()
    if (id !== undefined) {
      formData.append("id", id)
    }
    formData.append("country", patientData.country)
    formData.append("step", patientData.step)
    formData.append("first_name", patientData.first_name)
    formData.append("last_name", patientData.last_name)
    formData.append("dob", patientData.dob)
    formData.append("gender", patientData.gender)
    formData.append("ship_to_office", patientData.ship_to_office)
    formData.append("bill_to_office", patientData.bill_to_office)
    formData.append("plan_id", patientData.planid)

    const response = await fetch(`${API_ROUTES.PATIENT.ADD_PATIENT}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch {
    return null
  }
}

export const submitClinicalConditions = async (
  token: string,
  payload: {
    step: string
    id: string
    clinical_conditions: string
    general_notes: string
  },
): Promise<ClinicalConditionsResponse | null> => {
  console.log("🚀 ~ payload:", payload)
  try {
    const formData = new FormData()
    formData.append("step", payload.step)
    formData.append("id", payload.id)
    formData.append("clinical_conditions", JSON.stringify(payload.clinical_conditions))
    formData.append("general_notes", payload.general_notes)

    const response = await fetch(API_ROUTES.PATIENT.ADD_CLINICAL_DATA, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch {
    return null
  }
}

export const submitPatientData = async (
  token: string,
  id: string,
  records: {
    stlFile1: File
    stlFile2: File
    cbctFile: File
    profileRepose: File
    buccalRight: File
    buccalLeft: File
    frontalRepose: File
    frontalSmiling: File
    labialAnterior: File
    occlussalLower: File
    occlussalUpper: File
    radioGraph1?: File | null
    radioGraph2?: File | null
    generalRecords?: File[] | null
  },
): Promise<PatientDataResponse | null> => {
  const formData = new FormData()
  formData.append("step", "step3")
  formData.append("id", id)
  formData.append("stlFile1", records.stlFile1)
  formData.append("stlFile2", records.stlFile2)
  formData.append("cbctFile", records.cbctFile)
  formData.append("profileRepose", records.profileRepose)
  formData.append("buccalRight", records.buccalRight)
  formData.append("buccalLeft", records.buccalLeft)
  formData.append("frontalRepose", records.frontalRepose)
  formData.append("frontalSmiling", records.frontalSmiling)
  formData.append("labialAnterior", records.labialAnterior)
  formData.append("occlussalLower", records.occlussalLower)
  formData.append("occlussalUpper", records.occlussalUpper)
  formData.append("radioGraph1", records.radioGraph1 || new Blob())
  formData.append("radioGraph2", records.radioGraph2 || new Blob())

  if (records.generalRecords && records.generalRecords.length > 0) {
    records.generalRecords.forEach((file) => {
      formData.append(`generalRecords`, file);
    });
  }

  try {
    const response = await fetch(API_ROUTES.PATIENT.ADD_RECORDS_DATA, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch {
    return null
  }
}

export const submitRetainerPatientData = async (
  token: string,
  id: string,
  records: {
    stlFile1: File
    stlFile2: File
    cbctFile: File
    profileRepose: File
    buccalRight: File
    buccalLeft: File
    frontalRepose: File
    frontalSmiling: File
    labialAnterior: File
    occlussalLower: File
    occlussalUpper: File
    radioGraph1?: File | null
    radioGraph2?: File | null
    generalRecords?: File[] | null
  },
): Promise<PatientDataResponse | null> => {
  const formData = new FormData()
  formData.append("step", "step3")
  formData.append("id", id)
  formData.append("stlFile1", records.stlFile1)
  formData.append("stlFile2", records.stlFile2)
  // formData.append("cbctFile", records.cbctFile);
  formData.append("profileRepose", records.profileRepose)
  formData.append("buccalRight", records.buccalRight)
  formData.append("buccalLeft", records.buccalLeft)
  formData.append("frontalRepose", records.frontalRepose)
  formData.append("frontalSmiling", records.frontalSmiling)
  formData.append("labialAnterior", records.labialAnterior)
  formData.append("occlussalLower", records.occlussalLower)
  formData.append("occlussalUpper", records.occlussalUpper)
  // formData.append("radioGraph1", records.radioGraph1 || new Blob());
  // formData.append("radioGraph2", records.radioGraph2 || new Blob());

  // if (records.generalRecords && records.generalRecords.length > 0) {
  //   records.generalRecords.forEach((file, index) => {
  //     formData.append(`generalRecords[${index}]`, file);
  //   });
  // }

  try {
    const response = await fetch(API_ROUTES.PATIENT.ADD_RETAINER_PATIENT, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch {
    return null
  }
}

export const submitCasePrescription = async (
  token: string,
  id: string,
  casePrescriptionData: Record<string, unknown>,
  version: string,
): Promise<CasePrescriptionResponse | null> => {
  console.log("🚀 ~ version:", version)
  try {
    if (!token) {
      return null
    }

    const formData = new FormData()
    formData.append("step", "step4")
    formData.append("id", id)
    formData.append("case_prescription", JSON.stringify(casePrescriptionData))
    // formData.append("version", version);

    const response = await fetch(API_ROUTES.PATIENT.ADD_CASE_PERSCRIPTION, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch {
    return null
  }
}

export async function submitRetainerInfo({
  token,
  id,
  archRequired,
  sets,
  notes,
}: {
  token: string
  id: string | number
  archRequired: string[]
  sets: string | number
  notes: string
}) {
  const formData = new FormData()
  formData.append("step", "step5")
  formData.append("id", String(id))
  formData.append("archRequired", JSON.stringify(archRequired))
  formData.append("sets", String(sets))
  formData.append("notes", notes)

  const response = await fetch(API_ROUTES.PATIENT.ADD_RETAINER_PATIENT, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  })

  const result = await response.json();
  return result;
}

const getEmployeeBaseUrl = () => {
  const role = getDecryptedToken("Role")
  return role === "specialist" ? API_ROUTES.SPECIALIST_EMPLOYEE : API_ROUTES.EMPLOYEE
}

// Fetch employees/staff data from API
export const fetchEmployees = async (
  token: string,
  page = 1,
  limit = 10,
): Promise<{
  employees: unknown[]
  currentPage: number
  perPage: number
  totalItems: number
  totalPages: number
} | null> => {
  try {
    if (!token) {
      return null
    }
    const response = await fetch(`${API_SERVER_ROUTES.EMPLOYEE.GET_EMPLOYEES}?page=${page}&limit=${limit}`, {
      method: "GET",
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${token}`,
      },
    })
    if (!response.ok) {
      return null
    }
    const data = await response.json()
    // Adjust for nested data structure
    const d = data.data || {}
    return {
      employees: d.data || [],
      currentPage: d.currentPage || 1,
      perPage: d.perPage || 10,
      totalItems: d.totalItems || 0,
      totalPages: d.totalPages || 1,
    }
  } catch {
    return null
  }
}

// Update employee (edit)
export const updateEmployee = async (
  employeeId: string,
  data: {
    first_name: string
    last_name: string
    email: string
    salutation: string
    practice_phone_number: string
    mobile: string
    profession: string
  },
  token: string,
): Promise<unknown> => {
  try {
    if (!token) {
      toast.error("Unauthorized request")
      return null
    }

    // Use correct endpoint
    const role = getDecryptedToken("Role")
    const url =
      role === "specialist"
        ? `${API_SERVER_ROUTES.SPECIALIST_EMPLOYEE.GET_EMPLOYEES}/${employeeId}`
        : `${API_SERVER_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`

    // ✅ Use FormData like your original
    const formData = new FormData()
    formData.append("first_name", data.first_name)
    formData.append("last_name", data.last_name)
    formData.append("email", data.email)
    formData.append("salutation", data.salutation)
    formData.append("practice_phone_number", data.practice_phone_number)
    formData.append("mobile", data.mobile)
    formData.append("profession", data.profession)

    const response = await fetch(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      body: formData,
    })

    if (!response.ok) {
      let errorMsg = `Error updating employee: ${response.statusText}`
      try {
        const errorData = await response.json()
        if (errorData?.message) errorMsg = errorData.message
      } catch { }
      toast.error(errorMsg)
      return null
    }

    return await response.json()
  } catch (err) {
    console.error("Update employee error:", err)
    return null
  }
}

// ------------------ Create Employee ------------------
export const createEmployee = async (
  data: {
    first_name: string
    last_name: string
    email: string
    salutation: string
    practice_phone_number: string
    mobile: string
    profession: string
  },
  token: string,
): Promise<unknown> => {
  try {
    if (!token) {
      toast.error("Unauthorized request")
      return null
    }

    const formData = new FormData()
    formData.append("first_name", data.first_name)
    formData.append("last_name", data.last_name)
    formData.append("email", data.email)
    formData.append("salutation", data.salutation)
    formData.append("practice_phone_number", data.practice_phone_number)
    formData.append("mobile", data.mobile)
    formData.append("profession", data.profession)

    const response = await fetch(getEmployeeBaseUrl().CREATE_EMPLOYEE, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      body: formData,
    })

    if (!response.ok) {
      let errorMsg = `Error creating employee: ${response.statusText}`
      try {
        const errorData = await response.json()
        if (errorData?.message) errorMsg = errorData.message
      } catch { }
      toast.error(errorMsg)
    }

    return await response.json()
  } catch {
    return null
  }
}

// ------------------ Update Employee Status ------------------
export const updateEmployeeStatus = async (
  employeeId: string,
  status: "active" | "inactive",
  token: string,
): Promise<unknown> => {
  try {
    if (!token) {
      toast.error("Unauthorized request")
      return null
    }

    const body = new URLSearchParams()
    body.append("status", status)

    const response = await fetch(`${getEmployeeBaseUrl().GET_EMPLOYEES}/${employeeId}`, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
        accept: "application/json",
      },
      body,
    })

    if (!response.ok) {
      let errorMsg = `Error updating employee status: ${response.statusText}`
      try {
        const errorData = await response.json()
        if (errorData?.message) errorMsg = errorData.message
      } catch { }
      toast.error(errorMsg)
    }

    return await response.json()
  } catch {
    return null
  }
}

// ------------------ Fetch Employee By ID ------------------
export const fetchEmployeeById = async (
  id: string | number,
  token: string,
): Promise<EmployeeApiResponse["data"] | null> => {
  try {
    if (!token) {
      toast.error("Unauthorized request")
      return null
    }

    const response = await fetch(`${getEmployeeBaseUrl().GET_EMPLOYEE_BY_ID}/${id}`, {
      method: "GET",
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      return null
    }

    const data: EmployeeApiResponse = await response.json()
    return data.data
  } catch {
    return null
  }
}

// API function for creating patient
export const createRetainerPatient = async (
  token: string,
  patientData: {
    planid: string
    country: string
    step: string
    first_name: string
    last_name: string
    dob: string
    gender: string
    ship_to_office: string
    bill_to_office: string
  },
) => {
  try {
    const formData = new FormData()

    // Add all required fields to FormData
    formData.append("step", "step1")
    formData.append("first_name", patientData.first_name)
    formData.append("last_name", patientData.last_name)
    formData.append("dob", patientData.dob)
    formData.append("plan_id", patientData.planid)
    formData.append("gender", patientData.gender)
    formData.append("ship_to_office", patientData.ship_to_office)
    formData.append("bill_to_office", patientData.bill_to_office)

    const response = await fetch(API_ROUTES.RETAINER.CREATE_RETAINER_PATIENT, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      const errorData = await response.json()

      toast.error(errorData.message || "Failed to create patient")
    }

    return await response.json()
  } catch {
    return null
  }
}

// Add Address helper function:
export const addAddress = async (payload: AddAddressPayload): Promise<ApiResponse> => {
  try {
    const formData = new FormData()
    formData.append("clinic_name", payload.clinic_name)
    formData.append("street_address", payload.street_address)
    formData.append("city", payload.city)
    formData.append("postal_code", payload.postal_code)
    formData.append("phone_number", payload.phone_number)
    formData.append("address_type", payload.address_type)

    const res = await fetch(API_ROUTES.ADRESSES.ADD_ADDRESS, {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    })

    const data: ApiResponse = await res.json()
    if (!res.ok) {
      throw new Error(data.message || "Failed to add address")
    }

    return data
  } catch (error) {
    throw error
  }
}

// Delete Address helper function:
export const deleteAddress = async (payload: DeleteAddressPayload): Promise<ApiResponse> => {
  try {
    const res = await fetch(`${API_ROUTES.ADRESSES.DELETE_ADDRESS}/${payload.addressId}`, {
      method: "DELETE",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      credentials: "include",
    })

    const data: ApiResponse = await res.json()
    if (!res.ok) {
      throw new Error(data.message || "Failed to delete address")
    }

    return data
  } catch (error) {
    throw error
  }
}

// Update Address helper function:
interface UpdateAddressPayload {
  addressId: string | number
  clinic_name: string
  street_address: string
  city: string
  postal_code: string
  phone_number: string
  address_type: "bill_to" | "ship_to"
  token: string // JWT token from localStorage or wherever it's stored
}

export const updateAddress = async (payload: UpdateAddressPayload): Promise<ApiResponse> => {
  try {
    const formData = new FormData()
    formData.append("clinic_name", payload.clinic_name)
    formData.append("street_address", payload.street_address)
    formData.append("city", payload.city)
    formData.append("postal_code", payload.postal_code)
    formData.append("phone_number", payload.phone_number)
    formData.append("address_type", payload.address_type)

    const res = await fetch(`${API_ROUTES.ADRESSES.UPDATE_ADDRESS}/${payload.addressId}`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    })

    const data: ApiResponse = await res.json()
    if (!res.ok) {
      throw new Error(data.message || "Failed to update address")
    }

    return data
  } catch (error) {
    throw error
  }
}

export const fetchDoctorProfile = async (token: string): Promise<DoctorProfileApiResponse["data"] | null> => {
  try {
    if (!token) {
      return null
    }
    const response = await fetch(API_ROUTES.PROFILE.GET_PROFILE, {
      method: "GET",
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${token}`,
      },
    })
    if (!response.ok) {
      return null
    }
    const data: DoctorProfileApiResponse = await response.json()
    return data.data
  } catch {
    return null
  }
}

export const updateDoctorProfile = async (token: string, payload: UpdateDoctorProfilePayload): Promise<boolean> => {
  try {
    if (!token) {
      return false
    }
    const formData = new FormData()
    formData.append("first_name", payload.first_name)
    formData.append("last_name", payload.last_name)
    formData.append("email", payload.email)
    formData.append("username", payload.username)

    const response = await fetch(API_SERVER_ROUTES.PROFILE.GET_PROFILE, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })
    if (!response.ok) {
      await response.text()
      toast.error(`Error updating doctor profile: ${response.statusText}`)
      return false
    }
    return true
  } catch {
    return false
  }
}

export const submitRefinementRequest = async (
  patientId: string | number,
  token: string,
  payload: SubmitRefinementPayload,
): Promise<RefinementResponse | null> => {
  try {
    const formData = new FormData()
    formData.append("refinementDetails", payload.refinementDetails)
    formData.append("upperImpression", payload.upperImpression)
    formData.append("lowerImpression", payload.lowerImpression)
    formData.append("profileRepose", payload.profileRepose)
    formData.append("buccalRight", payload.buccalRight)
    formData.append("buccalLeft", payload.buccalLeft)
    formData.append("frontalRepose", payload.frontalRepose)
    formData.append("frontalSmiling", payload.frontalSmiling)
    formData.append("labialAnterior", payload.labialAnterior)
    formData.append("occlussalLower", payload.occlussalLower)
    formData.append("occlussalUpper", payload.occlussalUpper)
    formData.append("radioGraph1", payload.radioGraph1)
    formData.append("radioGraph2", payload.radioGraph2)

    const response = await fetch(`${API_ROUTES.PATIENT.ADD_REFINEMENT_ALIGNER}/${patientId}/refinements`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      await response.text()
      toast.error(`Error submitting refinement: ${response.statusText}`)
      return null
    }

    const data: RefinementResponse = await response.json()
    return data
  } catch {
    return null
  }
}

export const submitAlignerReplacement = async (
  token: string,
  replacementData: string,
  patientId: string | number,
): Promise<AlignerReplacementResponse | null> => {
  try {
    const params = new URLSearchParams()
    params.append("replacement_data", replacementData)
    params.append("patient_id", String(patientId))

    const response = await fetch(API_ROUTES.PATIENT.ADD_REPLACEMENT_ALIGNER, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Bearer ${token}`,
      },
      body: params.toString(),
    })

    if (!response.ok) {
      await response.text()
      toast.error(`Error submitting aligner replacement: ${response.statusText}`)
      return null
    }

    const data: AlignerReplacementResponse = await response.json()
    return data
  } catch {
    return null
  }
};

// helper check function
const isFile = (value: unknown): value is File | Blob => value instanceof File || value instanceof Blob;

export const submitRetainerRequest = async (
  token: string,
  payload: RetainerRequestPayload,
): Promise<RetainerRequestResponse | null> => {
  try {
    const formData = new FormData();
    formData.append("patient_id", String(payload.patient_id));
    formData.append("mode", payload.mode);

    if (payload.other_details) {
      formData.append("other_details", payload.other_details)
    }

    // ✅ Only append if it's a File/Blob, skip if string
    if (isFile(payload.stlFile1)) formData.append("stlFile1", payload.stlFile1);
    if (isFile(payload.stlFile2)) formData.append("stlFile2", payload.stlFile2);
    if (isFile(payload.cbctFile)) formData.append("cbctFile", payload.cbctFile);
    if (isFile(payload.profileRepose)) formData.append("profileRepose", payload.profileRepose);
    if (isFile(payload.buccalRight)) formData.append("buccalRight", payload.buccalRight);
    if (isFile(payload.buccalLeft)) formData.append("buccalLeft", payload.buccalLeft);
    if (isFile(payload.frontalRepose)) formData.append("frontalRepose", payload.frontalRepose);
    if (isFile(payload.frontalSmiling)) formData.append("frontalSmiling", payload.frontalSmiling);
    if (isFile(payload.labialAnterior)) formData.append("labialAnterior", payload.labialAnterior);
    if (isFile(payload.occlusalLower)) formData.append("occlussalLower", payload.occlusalLower);
    if (isFile(payload.occlusalUpper)) formData.append("occlussalUpper", payload.occlusalUpper);
    if (isFile(payload.radioGraph1)) formData.append("radioGraph1", payload.radioGraph1);
    if (isFile(payload.radioGraph2)) formData.append("radioGraph2", payload.radioGraph2);

    const response = await fetch(API_ROUTES.PATIENT.ADD_4DGRAPHY_RETAINER, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      console.error("Upload failed:", await response.text());
      return null;
    }

    const data: RetainerRequestResponse = await response.json();
    return data;
  } catch (err) {
    console.error("Error submitting retainer request:", err);
    return null;
  }
};


export const uploadCbctFile = async (token: string, payload: UploadCbctPayload): Promise<UploadCbctResponse | null> => {
  try {
    const formData = new FormData()
    formData.append("cbctFile", payload.cbctFile)
    formData.append("reason", payload.reason)

    const response = await fetch(`${API_ROUTES.PATIENT.ADD_CBCT_FILE}/${payload.patientId}/cbct`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    if (!response.ok) {
      await response.text()

      return null
    }

    const data: UploadCbctResponse = await response.json()
    return data
  } catch {
    return null
  }
}

export const changePassword = async (
  currentPassword: string,
  password: string,
  confirmPassword: string,
): Promise<ApiResponse | null> => {
  try {
    const token = getDecryptedToken("AccessToken")
    if (!token) {
      toast.error("Authentication token not found")
      return null
    }

    const formData = new FormData()
    formData.append("currentPassword", currentPassword)
    formData.append("password", password)
    formData.append("confirmPassword", confirmPassword)

    const response = await fetch(API_ROUTES.AUTH.CHANGE_PASSWORD, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    const data = await response.json()
    if (!response.ok) {
      return null
    }
    toast.success("Password changed successfully")
    return data
  } catch {
    return null
  }
}



export async function updatePatientStatus({
  patientId,
  status,
  token,
}: {
  patientId: number | string;
  status: string;
  token: string;
}): Promise<UpdatePatientStatusResponse> {
  try {
    const formData = new FormData();
    formData.append("status", status);

    const response = await fetch(`${API_ROUTES.PATIENT.ADD_UPDATE_STATUS}/${patientId}`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      }
    );

    const result = await response.json();
    return result;
  } catch {
    return { status: 500, success: false, message: "Failed to update status" };
  }
}
