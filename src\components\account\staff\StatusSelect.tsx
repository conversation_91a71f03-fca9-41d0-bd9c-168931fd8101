import React, { useEffect, useRef, useState } from "react";
import { updateEmployeeStatus } from "@/utils/ApisHelperFunction";
import { toast } from "react-toastify";
import { StaffMember } from "./StaffTable";
import { createPortal } from "react-dom";
import { getDecryptedToken } from "@/app/lib/auth";

const statusOptions = [
  { value: "active", label: "Active", color: "bg-orange-100 text-orange-600" },
  { value: "inactive", label: "Inactive", color: "bg-red-100 text-red-600" },
];

export interface StatusSelectProps {
  staff: {
    id: string;
    status: "active" | "inactive";
  };
  statusLoadingId: string | null;
  setStatusLoadingId: React.Dispatch<React.SetStateAction<string | null>>;
  onSearch?: (term: string) => void;
  onStatusChange?: (id: string, status: "active" | "inactive") => void;
  setFilteredData: React.Dispatch<React.SetStateAction<StaffMember[]>>;
}

const StatusSelect: React.FC<StatusSelectProps> = ({
  staff,
  statusLoadingId,
  setStatusLoadingId,
  onSearch,
  onStatusChange,
  setFilteredData,
}) => {
  const [open, setOpen] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 150 });
  const ref = useRef<HTMLDivElement>(null);

  /** ✅ Calculate dropdown position dynamically */
  const updatePosition = () => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 100;

      const shouldOpenUp =
        spaceBelow < dropdownHeight && spaceAbove > dropdownHeight;

      setPosition({
        top: shouldOpenUp
          ? rect.top + window.scrollY - dropdownHeight
          : rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  };

  /** ✅ Reposition on scroll or resize */
  useEffect(() => {
    const handleScrollOrResize = () => {
      if (open) {
        updatePosition();
      }
    };

    window.addEventListener("scroll", handleScrollOrResize, true);
    window.addEventListener("resize", handleScrollOrResize);

    return () => {
      window.removeEventListener("scroll", handleScrollOrResize, true);
      window.removeEventListener("resize", handleScrollOrResize);
    };
  }, [open]);

  useEffect(() => {
    if (open) updatePosition();
  }, [open]);

  /** ✅ Close on outside click */
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      const dropdown = document.getElementById(`dropdown-${staff.id}`);
      if (
        ref.current &&
        !ref.current.contains(e.target as Node) &&
        dropdown &&
        !dropdown.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleOutsideClick);

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [open, staff.id]);

  /** ✅ Close only on horizontal scroll */
  useEffect(() => {
    let lastScrollX = 0;

    const handleHorizontalScroll = (e: Event) => {
      const target = e.target as HTMLElement;

      if (target.scrollWidth > target.clientWidth) {
        if (Math.abs(target.scrollLeft - lastScrollX) > 0 && open) {
          setOpen(false);
        }
        lastScrollX = target.scrollLeft;
      }
    };

    const scrollContainers = document.querySelectorAll("*");
    scrollContainers.forEach((el) => {
      if ((el as HTMLElement).scrollWidth > (el as HTMLElement).clientWidth) {
        el.addEventListener("scroll", handleHorizontalScroll, true);
      }
    });

    return () => {
      scrollContainers.forEach((el) => {
        if ((el as HTMLElement).scrollWidth > (el as HTMLElement).clientWidth) {
          el.removeEventListener("scroll", handleHorizontalScroll, true);
        }
      });
    };
  }, [open]);

  /** ✅ Handle selecting a status */
  const handleSelect = async (newStatus: "active" | "inactive") => {
    setOpen(false);
    setStatusLoadingId(staff.id);
    const token = getDecryptedToken("AccessToken");
    if (!token) {
      toast.error("Authentication token missing. Please log in again.");
      setStatusLoadingId(null);
      return;
    }
    try {
      const result = await updateEmployeeStatus(staff.id, newStatus, token);
      if (
        result &&
        typeof result === "object" &&
        "success" in result &&
        result.success
      ) {
        toast.success("Status updated successfully");
        if (!onSearch) {
          setFilteredData((prev) =>
            prev.map((s) =>
              s.id === staff.id ? { ...s, status: newStatus } : s,
            ),
          );
        }
        onStatusChange?.(staff.id, newStatus);
      } else {
        toast.error("Failed to update status");
      }
    } catch {
    } finally {
      setStatusLoadingId(null);
    }
  };

  const currentLabel = statusOptions.find(
    (o) => o.value === staff.status,
  )?.label;

  return (
    <div
      className="relative w-full"
      onClick={(e) => e.stopPropagation()}
      ref={ref}
    >
      <button
        type="button"
        disabled={statusLoadingId === staff.id}
        onClick={() => setOpen((v) => !v)}
        className={`pl-4 pr-10 py-2 text-xs rounded-full outline-none w-full appearance-none flex items-center justify-between transition-colors ${
          staff.status === "active"
            ? "bg-green-100 text-green-800"
            : "bg-red-100 text-red-800"
        } ${statusLoadingId === staff.id ? "opacity-50 cursor-not-allowed" : ""}`}
      >
        <span>{currentLabel}</span>
        <span className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
          <svg
            className="w-4 h-4 text-gray-500"
            fill="none"
            stroke="currentColor"
            strokeWidth={2}
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </span>
        {statusLoadingId === staff.id && (
          <span className="absolute inset-y-0 right-8 flex items-center">
            <svg
              className="animate-spin h-4 w-4 text-primary"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth={4}
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8v8z"
              />
            </svg>
          </span>
        )}
      </button>

      {open &&
        createPortal(
          <ul
            id={`dropdown-${staff.id}`}
            className="absolute z-[99999] mt-1 rounded-2xl bg-white shadow-lg ring-1 ring-[#ffe5d4] ring-opacity-5"
            style={{
              top: position.top,
              left: position.left,
              minWidth: position.width,
            }}
          >
            {statusOptions.map((opt) => (
              <li
                key={opt.value}
                onClick={() => handleSelect(opt.value as "active" | "inactive")}
                className={`cursor-pointer select-none relative py-2 px-4 rounded-2xl transition
                                    ${
                                      staff.status === opt.value
                                        ? opt.color + " font-bold"
                                        : "text-gray-900"
                                    }
                                    hover:bg-primary/10 hover:text-primary`}
              >
                {opt.label}
              </li>
            ))}
          </ul>,
          document.body,
        )}
    </div>
  );
};

export default StatusSelect;
