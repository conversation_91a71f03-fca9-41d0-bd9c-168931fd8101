import { API_ROUTES } from "@/utils/ApiRoutes";
import { EmployeeApiResponse } from "@/types/types";
import { getDecryptedToken } from "@/app/lib/auth";

export const fetchEmployees = async (
  page: number = 1,
  limit: number = 10
): Promise<EmployeeApiResponse> => {
  const token = getDecryptedToken("AccessToken");
  const url = `${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}?page=${page}&limit=${limit}`;

  try {
    const res = await fetch(url, {
      method: "GET",
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await res.json();

    if (!res.ok) {
      return data;
    } else {
      return data as EmployeeApiResponse;
    }
  } catch (error) {
    console.error("Error fetching employees:", error);

    return {
      status: 500,
      success: false,
      data: {
        data: [],
        id: 0,
        first_name: "",
        last_name: "",
        email: "",
        username: "",
        profile_image: null,
        role: "",
        salutation: "",
        practice_phone_number: "",
        mobile: "",
        profession: ""
      },
      message: "Unable to fetch employees",
    };
  }
};

// Update employee (edit)
// export const updateEmployee = async (
//   employeeId: string,
//   data: {
//     first_name: string;
//     last_name: string;
//     email: string;
//     salutation: string;
//     practice_phone_number: string;
//     mobile: string;
//     profession: string;
//   },
//   token: string
// ): Promise<unknown> => {
//   try {
//     if (!token) {

//     }
//     const formData = new FormData();
//     formData.append('first_name', data.first_name);
//     formData.append('last_name', data.last_name);
//     formData.append('email', data.email);
//     formData.append('salutation', data.salutation);
//     formData.append('practice_phone_number', data.practice_phone_number);
//     formData.append('mobile', data.mobile);
//     formData.append('profession', data.profession);

//     const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`, {
//       method: 'PUT',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'accept': 'application/json',
//       },
//       body: formData,
//     });
//     if (!response.ok) {
//       let errorMsg = `Error updating employee: ${response.statusText}`;
//       try {
//         const errorData = await response.json();
//         if (errorData && errorData.message) {
//           errorMsg = errorData.message;
//         }
//       } catch {}
//       toast.error(errorMsg);
//     }
//     const result = await response.json();
//     toast.success("Staff updated successfully");
//     return result;
//   }
//   catch (error) {
//     toast.error("Failed to update Staff");
//    return error;
//   }
// };

// // Create employee (add)
// export const createEmployee = async (
//   data: {
//     first_name: string;
//     last_name: string;
//     email: string;
//     salutation: string;
//     practice_phone_number: string;
//     mobile: string;
//     profession: string;
//   },
//   token: string
// ): Promise<unknown> => {
//   try {
//     if (!token) {
//       throw new Error('Authorization token is missing');
//     }
//     const formData = new FormData();
//     formData.append('first_name', data.first_name);
//     formData.append('last_name', data.last_name);
//     formData.append('email', data.email);
//     formData.append('salutation', data.salutation);
//     formData.append('practice_phone_number', data.practice_phone_number);
//     formData.append('mobile', data.mobile);
//     formData.append('profession', data.profession);

//     const response = await fetch(API_ROUTES.EMPLOYEE.CREATE_EMPLOYEE, {
//       method: 'POST',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'accept': 'application/json',
//       },
//       body: formData,
//     });
//     if (!response.ok) {
//       let errorMsg = `Error creating employee: ${response.statusText}`;
//       try {
//         const errorData = await response.json();
//         if (errorData && errorData.message) {
//           errorMsg = errorData.message;
//         }
//       } catch {}
//       throw new Error(errorMsg);
//     }
//     const result = await response.json();
//     toast.success("Staff created successfully");
//     return result;
//   } catch (error) {
//     // Show the specific error message from the backend if available
//     const errorMessage = error || "Failed to create staff";
//     toast.error(`${errorMessage}`);
//     throw error;
//   }
// };

// // Update employee status (active/inactive)
// export const updateEmployeeStatus = async (
//   employeeId: string,
//   status: 'active' | 'inactive',
//   token: string
// ): Promise<unknown> => {
//   try {
//     if (!token) {
//       throw new Error('Authorization token is missing');
//     }
//     const body = new URLSearchParams();
//     body.append('status', status);
//     const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`, {
//       method: 'PATCH',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'Content-Type': 'application/x-www-form-urlencoded',
//         'accept': 'application/json',
//       },
//       body,
//     });
//     if (!response.ok) {
//       let errorMsg = `Error updating employee status: ${response.statusText}`;
//       try {
//         const errorData = await response.json();
//         if (errorData && errorData.message) {
//           errorMsg = errorData.message;
//         }
//       } catch {}
//       throw new Error(errorMsg);

//     }
//     const result = await response.json();

//     return result;
//   } catch (error) {
//     toast.error("Failed to update staff status");
//     throw error;
//   }
// };

// // Server-side fetch for single employee by ID
// export const fetchEmployeeById = async (
//   id: string | number
// ): Promise<EmployeeApiResponse['data'] | null> => {
//   const url = `${API_ROUTES.EMPLOYEE.GET_EMPLOYEE_BY_ID}/${id}`;
//   const d = await fetchApi<EmployeeApiResponse>(url);
//   return d?.data || null;
// };
