"use client";

import { getDecryptedToken } from "@/app/lib/auth";
import React, { useState } from "react";

interface Props {
  onClose: () => void;
  versionId: number;
  onReviewed?: () => void;
}

interface FieldErrors {
  upperSteps?: string;
  lowerSteps?: string;
  sharedLink?: string;
  reason?: string;
}

const ReviewVersionModal: React.FC<Props> = ({
  onClose,
  versionId,
  onReviewed,
}) => {
  const [action, setAction] = useState<"accepted_by_specialist" | "reject">("accepted_by_specialist");
  const [upperSteps, setUpperSteps] = useState("");
  const [lowerSteps, setLowerSteps] = useState("");
  const [sharedLink, setSharedLink] = useState("");
  const [reason, setReason] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [fieldErrors, setFieldErrors] = useState<FieldErrors>({});

  const handleSubmit = async () => {
    setLoading(true);
    setError("");
    setFieldErrors({});

    // Validate field lengths
    let hasErrors = false;
    const newFieldErrors: Record<string, string> = {};

    if (upperSteps && upperSteps.length > 255) {
      newFieldErrors.upperSteps = "Maximum length exceeded (255 characters)";
      hasErrors = true;
    }
    if (lowerSteps && lowerSteps.length > 255) {
      newFieldErrors.lowerSteps = "Maximum length exceeded (255 characters)";
      hasErrors = true;
    }
    if (reason && reason.length > 255) {
      newFieldErrors.reason = "Maximum length exceeded (255 characters)";
      hasErrors = true;
    }
    if (sharedLink && sharedLink.length > 255) {
      newFieldErrors.sharedLink = "Maximum length exceeded (255 characters)";
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(newFieldErrors);
      setLoading(false);
      return;
    }

    const formData = new FormData();
    formData.append("action", action);
    if (upperSteps) formData.append("upper_steps", upperSteps);
    if (lowerSteps) formData.append("lower_steps", lowerSteps);
    if (reason) formData.append("reason", reason);
    if (action === "accepted_by_specialist") {
      if (!sharedLink) {
        setError("Shared link is required when approving.");
        setLoading(false);
        return;
      }
      formData.append("shared_link", sharedLink);
    }

    try {
      const token = await getDecryptedToken("AccessToken");

      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/specialist/patient-versions/${versionId}/review`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        },
      );
      const result = await res.json();
      if (!result.success) {
        setError(result.message || "Something went wrong");
      } else {
        onReviewed?.();
        onClose();
        window.location.reload(); // Reload the page after successful submission
      }
    } catch {
      setError("Failed to submit review.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/20 z-50 flex items-center justify-center p-2"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-2xl shadow-xl w-full max-w-xl overflow-y-auto scrollbar-hidden h-[90vh]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800">
            Review Patient Version
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
            aria-label="Close modal"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div className="space-y-6">
            <div>
              <label
                htmlFor="action-select"
                className="block text-lg font-medium text-gray-800 mb-3"
              >
                Action
              </label>
              <select
                id="action-select"
                name="action"
                aria-label="Select review action"
                value={action}
                onChange={(e) =>
                  setAction(e.target.value as "accepted_by_specialist" | "reject")
                }
                className="w-full border border-gray-300 rounded-lg p-4 focus:ring-2 focus:ring-primary focus:border-primary"
              >
                <option value="accepted_by_specialist">Approve</option>
                <option value="reject">Reject</option>
              </select>
            </div>

            <div>
              <label className="block text-lg font-medium text-gray-800 mb-3">
                Steps Information
              </label>
              <div className="mb-4">
                <input
                  type="number"
                  min="0"
                  max="100"
                  className="w-full border border-gray-300 rounded-lg p-4 focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="Upper Steps (optional)"
                  value={upperSteps}
                  onChange={(e) => {
                    const value = e.target.value;
                    const numValue = parseInt(value);
                    if (value === "" || (numValue >= 0 && numValue <= 100)) {
                      setUpperSteps(value);
                    }
                  }}
                />
                {fieldErrors.upperSteps && (
                  <p className="text-red-500 text-sm mt-1">
                    {fieldErrors.upperSteps}
                  </p>
                )}
              </div>
              <div>
                <input
                  type="number"
                  min="0"
                  max="100"
                  className="w-full border border-gray-300 rounded-lg p-4 focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="Lower Steps (optional)"
                  value={lowerSteps}
                  onChange={(e) => {
                    const value = e.target.value;
                    const numValue = parseInt(value);
                    if (value === "" || (numValue >= 0 && numValue <= 100)) {
                      setLowerSteps(value);
                    }
                  }}
                />
                {fieldErrors.lowerSteps && (
                  <p className="text-red-500 text-sm mt-1">
                    {fieldErrors.lowerSteps}
                  </p>
                )}
              </div>
            </div>

            {action === "accepted_by_specialist" && (
              <div>
                <label className="block text-lg font-medium text-gray-800 mb-3">
                  Shared Link
                </label>
                <div>
                  <input
                    className="w-full border border-gray-300 rounded-lg p-4 focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Shared Link (required)"
                    maxLength={255}
                    value={sharedLink}
                    onChange={(e) => {
                      const value = e.target.value;
                      setSharedLink(value);
                      setFieldErrors((prev) => ({
                        ...prev,
                        sharedLink:
                          value.length > 255
                            ? "Maximum length exceeded (255 characters)"
                            : undefined,
                      }));
                    }}
                  />
                  {fieldErrors.sharedLink && (
                    <p className="text-red-500 text-sm mt-1">
                      {fieldErrors.sharedLink}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div>
              <label className="block text-lg font-medium text-gray-800 mb-3">
                Additional Information
              </label>
              <div>
                <textarea
                  className="w-full border border-gray-300 rounded-lg p-4 h-32 focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder={
                    action === "reject"
                      ? "Reason (required)"
                      : "Reason (optional)"
                  }
                  maxLength={255}
                  value={reason}
                  onChange={(e) => {
                    const value = e.target.value;
                    setReason(value);
                    setFieldErrors((prev) => ({
                      ...prev,
                      reason:
                        value.length > 255
                          ? "Maximum length exceeded (255 characters)"
                          : undefined,
                    }));
                  }}
                />
                {fieldErrors.reason && (
                  <p className="text-red-500 text-sm mt-1">
                    {fieldErrors.reason}
                  </p>
                )}
              </div>
            </div>
          </div>

          {error && <p className="text-red-600 text-sm mt-4">{error}</p>}
        </div>

        <div className="border-t border-gray-200 p-6 flex justify-end gap-4">
          <button
            type="button"
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium cursor-pointer hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-6 py-2 rounded-full bg-primary text-white font-medium cursor-pointer hover:bg-primary/90 disabled:opacity-50"
          >
            {loading ? "Submitting..." : "Submit"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReviewVersionModal;
