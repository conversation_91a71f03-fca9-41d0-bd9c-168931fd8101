"use client";
import { useState } from "react";
import LongVersion from "./versions/LongVersion";
import ShortVersion from "./versions/ShortVersion";
import { PatientCaseData, PatientFileData } from "@/types/types";

interface CasePrescriptionProps {
  patientData: PatientFileData;
}

const CasePrescription = ({ patientData }: CasePrescriptionProps) => {
  const Caseinfo = patientData?.data;
  const [tab, setTab] = useState<"long" | "short">(
    patientData?.data?.version === "long" ? "long" : "short",
  );

  return (
    <div>
      <div className="flex gap-4 mb-4">
        <button
          className={`px-4 py-1.5 rounded ${tab === "long" ? "bg-primary text-white" : "bg-gray-200"}`}
          onClick={() => setTab("long")}
        >
          Long Version
        </button>
        <button
          className={`px-4 py-1.5 rounded ${tab === "short" ? "bg-primary text-white" : "bg-gray-200"}`}
          onClick={() => setTab("short")}
        >
          Short Version
        </button>
      </div>
      {tab === "long" && (
        <LongVersion patientData={Caseinfo as PatientCaseData} />
      )}
      {tab === "short" && (
        <ShortVersion patientData={Caseinfo as PatientCaseData} />
      )}
    </div>
  );
};

export default CasePrescription;
