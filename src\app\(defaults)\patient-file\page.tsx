import { fetchApi, getServerCookies } from "@/api/getapis";
import PatientFile from "@/components/patient-file/PatientFile";
import { PatientFileData, Specialist } from "@/types/types";
import { API_ROUTES } from "@/utils/ApiRoutes";
import React from "react";

interface PageProps {
  params: Promise<{ slug?: string }>;
  searchParams: Promise<{ id?: string }>;
}

const Page = async ({ searchParams }: PageProps) => {
  const resolvedSearchParams = await searchParams;
  const patientId = resolvedSearchParams.id ?? "";

  let patientData: PatientFileData | null = null;
  let specialistData: Specialist | null = null;
  if (patientId) {
    // Determine user role to select correct API route
    const role = await getServerCookies("Role");
    // console.log("🚀 ~ page ~ role:", role);

    // Employees should use doctor endpoints, specialists use specialist endpoints
    const baseRoute =
      role === "specialist"
        ? API_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST
        : API_ROUTES.PATIENT.GET_PATIENT_BY_ID;

    const url = `${baseRoute}/${patientId}`;
    const patientsArray = (await fetchApi(url)) as PatientFileData;
    const specialist = await fetchApi(
      `${API_ROUTES.PATIENT.GET_SPECIALIST}/${patientsArray.id}/specialist`,
    );
    // console.log("🚀 ~ page ~ specialist:", specialist);
    // console.log("🚀 ~ page ~ patientsArray:", patientsArray);
    patientData = patientsArray;
    // Ensure specialist data matches the Specialist type or is null
    specialistData =
      specialist && typeof specialist === "object" && "id" in specialist
        ? (specialist as Specialist)
        : null;
  }

  return (
    <>
      {/* {specialistData && ( */}
      <PatientFile
        data={patientData as PatientFileData}
        patientId={patientId}
        specialist={specialistData as Specialist}
      />
      {/* )} */}
    </>
  );
};

export default Page;
