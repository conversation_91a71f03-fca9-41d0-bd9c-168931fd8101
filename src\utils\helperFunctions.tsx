export function capitalizeFirstWord(str: string) {
  if (!str) return str;
  return str.replace(/^\s*\w/, (c) => c.toUpperCase());
}

// export function formatStatus(status: string): string {
//   if (!status) return "";
//   // Replace underscores with spaces and capitalize first letter of each word
//   return status
//     .replace(/_/g, " ")
//     .replace(/\b\w/g, (char) => char.toUpperCase());
// }

export function formatCountry(country: unknown): string {
  if (!country || typeof country !== "string") return "";
  const cleaned = country.replace(/[-_]/g, " ").trim();
  return cleaned
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}