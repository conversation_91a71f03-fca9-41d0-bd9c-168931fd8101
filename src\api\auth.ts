import { API_ROUTES } from "@/utils/ApiRoutes";
import { LoginResponse, ForgetPasswordResponse } from "@/types/types";

export const loginUser = async (
  identifier: string,
  password: string,
  remember?: boolean,
): Promise<LoginResponse | null> => {
  try {
    const formData = new FormData();
    const isEmail = identifier.includes("@") && identifier.includes(".com");

    // Add appropriate field based on identifier type
    if (isEmail) {
      formData.append("email", identifier);
    } else {
      formData.append("username", identifier);
    }

    formData.append("password", password);
    if (remember) {
      formData.append("remember", remember.toString());
    }

    const response = await fetch(`${API_ROUTES.AUTH.LOGIN}`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    if (!data.success) {
      return data;
    }
    return data;
  } catch {
    return null;
  }
};

export const forgetPassword = async (
  identifier: string,
): Promise<ForgetPasswordResponse | null> => {
  try {
    const formData = new FormData();
    const isEmail = identifier.includes("@") && identifier.includes(".");

    if (isEmail) {
      formData.append("email", identifier);
    } else {
      formData.append("username", identifier);
    }

    const response = await fetch(`${API_ROUTES.AUTH.FORGET_PASSWORD}`, {
      method: "POST",
      headers: {
        accept: "application/json",
      },
      body: formData,
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data;
  } catch {
    return null;
  }
};
