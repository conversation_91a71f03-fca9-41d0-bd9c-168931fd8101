import { API_ROUTES, API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import {
  DoctorProfileApiResponse,
  ProfileData,
  UpdateProfileResponse,
  UpdateSpecialistProfilePayload,
} from "@/types/types";
import { toast } from "react-toastify";
import { getDecryptedToken } from "@/app/lib/auth";

export const fetchSpecialistProfile =
  async (): Promise<DoctorProfileApiResponse> => {
    const url = API_SERVER_ROUTES.PROFILE.GET_PROFILE;
    try {
      const token = await getDecryptedToken("AccessToken");

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle non-200 responses (e.g., 401, 500)
        console.error("API Error:", data);
        return {
          status: response.status,
          success: false,
          profile_image: "",
          email: "",
          last_name: "",
          first_name: "",
          username: "",
          data: {} as ProfileData,
          message: data.message || "Failed to fetch profile",
        };
      }

      return {
        status: response.status,
        success: true,
        profile_image: data.profile_image || "",
        email: data.email || "",
        last_name: data.last_name || "",
        first_name: data.first_name || "",
        username: data.username || "",
        data: data.data,
        message: data.message || "Profile fetched successfully",
      };
    } catch (error) {
      console.error("Fetch Error:", error);
      return {
        status: 500,
        success: false,
        profile_image: "",
        email: "",
        last_name: "",
        first_name: "",
        username: "",
        data: {} as ProfileData,
        message: "An unexpected error occurred",
      };
    }
  };

export const updateSpecialistProfile = async (
  token: string,
  payload: UpdateSpecialistProfilePayload
): Promise<UpdateProfileResponse | null> => {
  try {
    if (!token) {
      toast.error("Unauthorized — missing token");
      return null;
    }

    const formData = new FormData();
    formData.append("first_name", payload.first_name);
    formData.append("last_name", payload.last_name);
    formData.append("email", payload.email);
    formData.append("username", payload.username);

    if (payload.profileImage) {
      formData.append("profile_image", payload.profileImage);
    }

    const response = await fetch(API_ROUTES.PROFILE.UPDATE_PROFILE, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Specialist profile update failed:", errorText);
      toast.error("Error updating specialist profile");
      return null;
    }

    // ✅ parse backend response like doctor update
    const result = await response.json();
    console.log("Specialist profile update success:", result);

    return result; // return full backend response instead of true/false
  } catch (error) {
    console.error("Specialist profile update error:", error);
    toast.error("Profile update failed!");
    return null;
  }
};

export const deleteSpecialistProfileImage = async (userId: number, fileKey: string): Promise<boolean> => {
  try {
    const token = await getDecryptedToken("AccessToken")

    if (!token) {
      toast.error("Authentication required")
      return false
    }

    const params = new URLSearchParams()
    params.append("record_id", userId.toString())
    params.append("fieldName", "profile_image") // Try "stlFile1" if this doesn't work
    params.append("tableName", "users") // Try without encryption first
    params.append("fileKey", fileKey)

    console.log("[v0] Delete API Parameters:", {
      record_id: userId.toString(),
      fieldName: "profile_image",
      tableName: "users",
      fileKey: fileKey,
      url: API_ROUTES.PROFILE.DELETE_FILE,
    })

    const response = await fetch(API_ROUTES.PROFILE.DELETE_FILE, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: params,
    })

    const responseData = await response.json()
    console.log("[v0] Delete API Response:", responseData)

    if (!response.ok) {
      toast.error(`Failed to delete profile image: ${responseData.message || "Unknown error"}`)
      return false
    }

    toast.success("Profile image removed successfully")
    return true
  } catch (error) {
    console.error("Delete Error:", error)
    toast.error("An error occurred while deleting the image")
    return false
  }
};
