@import url("https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&display=swap");
@import "tailwindcss";

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

select {
  -webkit-appearance: none; /* Safari/Chrome */
  -moz-appearance: none; /* Firefox */
  appearance: none; /* Modern browsers */
  background: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

body {
  font-family: "Manrope", sans-serif;
}
.background {
  background-image: url("../../public/images/BGauth.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
   min-height: 100vh;
  width: 100%;
}

/* Flatpickr main color override */
.flatpickr-calendar {
  --flatpickr-primary: #d45a08;
}

.flatpickr-months {
  background: #d45a08 !important;
  color: #fff !important;
}
.flatpickr-month {
  background: #d45a08 !important;
  color: #fff !important;
}
.flatpickr-weekdays {
  background: #d45a08 !important;
  color: #fff !important;
}
.flatpickr-weekday {
  background: #d45a08 !important;
  color: #fff !important;
}
.flatpickr-current-month
  .flatpickr-monthDropdown-months
  .flatpickr-monthDropdown-month {
  background: #f5f5f5 !important;
  color: #000 !important;
}
.flatpickr-monthDropdown-months {
  background: #d45a08 !important;
  color: #fff !important;
}

.flatpickr-day.selected,
.flatpickr-day .selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day:focus {
  background: #d45a08 !important;
  color: #fff !important;
}
.flatpickr-day.today.selected {
  background: #d45a08 !important;
  color: #fff !important;
}
.flatpickr-day.today {
  background: transparent !important;
  border-color: #d45a08 !important;
  color: #000 !important;
}

.flatpickr-weekday {
  color: #d45a08;
}

@theme {
  --color-dark: #444443;
  --color-gray: #999999;
  --color-danger: #ff0000;
  --color-primary: #eb6309;
  --color-primaryLight: #fff6f0;
  --color-orange: #eb6309;
}

.custom-shadow {
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
[data-popover][data-open] [data-popover-content] {
  display: block;
}