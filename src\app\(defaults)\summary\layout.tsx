"use client";

import '@/app/globals.css';
import { useState } from 'react';
import Header from '@/components/reuseable/Header';

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    // ✅ Parent state — single source of truth
    const [search, setSearch] = useState('');

    return (
        <div className="py-2 xl:px-12 px-8 flex-grow flex flex-col">
            {/* Pass state + updater to Header */}
            <Header 
                searchValue={search}       // Always the parent state
                onSearchChange={setSearch} // Updates parent when typing
            />

            <div className='pt-6 pb-2 flex-grow flex flex-col'>
                {children}
            </div>
        </div>
    );
}
