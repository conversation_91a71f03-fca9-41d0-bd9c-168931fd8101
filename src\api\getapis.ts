import { cookies } from 'next/headers';

// Import crypto-js properly for server-side usage
const getCryptoJS = async () => {
  if (typeof window === 'undefined') {
    // Server-side
    const CryptoJS = await import('crypto-js');
    return CryptoJS.default || CryptoJS;
  } else {
    // Client-side
    const CryptoJS = await import('crypto-js');
    return CryptoJS.default || CryptoJS;
  }
};

interface ApiResponse<T> {
  data?: T;
}

export async function getServerCookies(value: string): Promise<string> {
  try {
    const _key = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "7d7cd92a9c";
    // console.log("🔑 Encryption Key:", _key);

    const cookieStore = await cookies();
    const encryptedToken = cookieStore.get(value);

    // console.log("🍪 Encrypted cookie:", encryptedToken);

    if (!encryptedToken?.value) return "";

    try {
      const CryptoJS = await getCryptoJS();
      const decryptedBytes = CryptoJS.AES.decrypt(encryptedToken.value, _key);
      
      if (!decryptedBytes) {
        console.error("❌ Decryption failed: no bytes returned");
        return "";
      }

      const decryptedString = decryptedBytes.toString(CryptoJS.enc.Utf8);

      if (!decryptedString) {
        console.error("❌ Decryption failed: Empty string (maybe wrong key?)");
        return "";
      }

      // console.log("✅ Decrypted cookie value:", decryptedString);
      return decryptedString;
    } catch (err) {
      console.error("❌ Decryption error:", err);
      return "";
    }
  } catch (err) {
    console.error("❌ getServerCookies Error:", err);
    return "";
  }
}


export const fetchApi = async <T>(url: string): Promise<T | null> => {
    try {
        const token = await getServerCookies('AccessToken');
        // console.log("===============>",token)
        const headers = token ? { Authorization: `Bearer ${token}` } : undefined;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                ...(headers || {}),
                'Cache-Control': 'no-cache',
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            return null;
        }


        
        const data: ApiResponse<T> = await response.json();

        if (data.data !== undefined) {
            return data.data;
        } else {
            return null;
        }
    } catch {
        return null;
    }

};
