import React, { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Image, { StaticImageData } from "next/image";
import { DentalPhoto } from "../patient-records/PatientRecords";
// import { FaExternalLinkAlt } from "react-icons/fa";

import buccalLeft from "../../../public/svgs/buccal-left.svg";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import logo from "../../../public/images/logo.png";
import { toast } from "react-toastify";
import { PatientFileData } from "@/types/types";
import { submitRetainerRequest } from "@/utils/ApisHelperFunction"; // Add this import at the top
import { getDecryptedToken } from "@/app/lib/auth";

interface RetainerRequestModalProps {
  onClose: () => void;
  patientId: string;
  patientData: PatientFileData;
}

const fileSchema = z.instanceof(File).nullable();

// Define schema with added scan validation
const retainerSchema = z
  .object({
    sets: z.number().min(1, "At least 1 set is required"),
    archRequired: z
      .array(z.enum(["upper", "lower"]))
      .min(1, "Select at least one arch"),
    notes: z.string().optional(),
    scanOption: z.enum(["upload_new", "use_last"]),
    photos: z
      .array(
        z.object({
          name: z.string(),
          file: fileSchema,
        }),
      )
      .optional(),
    upperSTL: fileSchema,
    lowerSTL: fileSchema,
  })
  .refine(
    (data) => {
      // If uploading new scans, both STLs must be provided
      if (data.scanOption === "upload_new") {
        return data.upperSTL !== null && data.lowerSTL !== null;
      }
      return true;
    },
    {
      message:
        "Both upper and lower STL files are required when uploading new scans",
      path: ["upperSTL"],
    },
  );

type RetainerFormValues = z.infer<typeof retainerSchema>;

const RetainerRequestModal: React.FC<RetainerRequestModalProps> = ({
  onClose,
  patientId,
  patientData,
}) => {
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1);
  const [photoUrls, setPhotoUrls] = useState<Record<string, string>>({});

  // Photo placeholders mapping
  const photoPlaceholders: Record<string, StaticImageData> = {
    profileRepose: profileRepose,
    frontalRepose: frontalRepose,
    frontalSmiling: frontalSmiling,
    occlussalUpper: occlussalUpper,
    socialSmile: logo,
    occlussalLower: occlussalLower,
    buccalRight: buccalRight,
    labialAnterior: labialAnterior,
    buccalLeft: buccalLeft,
  };

  // Initialize photos state
  const [photos, setPhotos] = useState<DentalPhoto[]>([
    { name: "profileRepose", file: null },
    { name: "frontalRepose", file: null },
    { name: "frontalSmiling", file: null },
    { name: "occlussalUpper", file: null },
    { name: "socialSmile", file: null },
    { name: "occlussalLower", file: null },
    { name: "buccalRight", file: null },
    { name: "labialAnterior", file: null },
    { name: "buccalLeft", file: null },
  ]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    trigger,
  } = useForm<RetainerFormValues>({
    resolver: zodResolver(retainerSchema),
    defaultValues: {
      sets: 1,
      archRequired: [],
      notes: "",
      scanOption: "upload_new",
      photos: photos.map((photo) => ({
        name: photo.name,
        file: typeof photo.file === "string" ? null : photo.file,
      })),
      upperSTL: null,
      lowerSTL: null,
    },
    mode: "onChange",
  });

  const sets = watch("sets");
  const scanOption = watch("scanOption");
  const upperSTL = watch("upperSTL");
  const lowerSTL = watch("lowerSTL");
  const archRequired = watch("archRequired");
  const notes = watch("notes");

  // Create object URLs for photos when files are added/removed
  useEffect(() => {
    const urls: Record<string, string> = {};
    photos.forEach((photo) => {
      if (photo.file) {
        if (typeof photo.file === "string") {
          urls[photo.name] = photo.file;
        } else if (photo.file instanceof File) {
          urls[photo.name] = URL.createObjectURL(photo.file);
        }
      }
    });
    setPhotoUrls(urls);

    // Cleanup function to revoke object URLs
    return () => {
      Object.values(urls).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [photos]);

  // Handle photo upload
  const handlePhotoFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    photoName: string,
  ) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      // Update photos state
      const updatedPhotos = photos.map((photo) =>
        photo.name === photoName ? { ...photo, file } : photo,
      );
      setPhotos(updatedPhotos);
      // Update form value
      setValue(
        "photos",
        updatedPhotos.map((photo) => ({
          name: photo.name,
          file: typeof photo.file === "string" ? null : photo.file,
        })),
      );
    }
  };

  // Handle photo deletion
  const handleDeletePhoto = (photoName: string) => {
    const updatedPhotos = photos.map((photo) =>
      photo.name === photoName ? { ...photo, file: null } : photo,
    );
    setPhotos(updatedPhotos);
    setValue(
      "photos",
      updatedPhotos.map((photo) => ({
        name: photo.name,
        file: typeof photo.file === "string" ? null : photo.file,
      })),
    );
  };

  // Handle STL file changes
  const handleSTLFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: "upperSTL" | "lowerSTL",
  ) => {
    if (e.target.files && e.target.files[0]) {
      setValue(field, e.target.files[0]);
    }
  };

  // Get image source for a photo
  const getImage = (photo: DentalPhoto): string | StaticImageData => {
    if (photo.file) {
      return photoUrls[photo.name] || photoPlaceholders[photo.name];
    }
    return photoPlaceholders[photo.name];
  };

  // Format photo name for display
  const formatPhotoName = (name: string): string => {
    return name
      .replace(/([A-Z])/g, " $1") // Add space before capital letters
      .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
  };

  // Handle next step with validation
  const handleNextStep = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent any default behavior

    if (currentStep === 1) {
      // Check if archRequired has at least one selection
      if (!archRequired || archRequired.length === 0) {
        await trigger("archRequired"); // Trigger validation to show error
        return;
      }
      // Advance to next step
      setCurrentStep(2);
    } else if (currentStep === 2) {
      // Validate STL files if upload_new is selected
      if (scanOption === "upload_new") {
        const isValid = await trigger(["upperSTL", "lowerSTL"]);
        if (!isValid) return;
      }
      // Advance to summary step
      setCurrentStep(3);
    }
  };

  const handleBack = (e: React.MouseEvent) => {
    e.preventDefault();
    if (currentStep === 2) {
      setCurrentStep(1);
    } else if (currentStep === 3) {
      setCurrentStep(2);
    }
  };

  const onSubmitForm = async (data: RetainerFormValues) => {
    console.log("🚀 ~ onSubmitForm ~ data:", data);
    let submissionPhotos: { name: string; file: File | string | null }[] = [];

    if (data.scanOption === "use_last") {
      submissionPhotos = photos.map((photo) => {
        if (photo.name === "socialSmile") {
          return { name: photo.name, file: null };
        }
        const url = getPatientPhotoUrl(photo.name as PhotoFieldName);
        return { name: photo.name, file: typeof url === "string" ? url : null };
      });
    } else {
      submissionPhotos = photos.map((photo) => ({
        name: photo.name,
        file: photo.file,
      }));
    }

    const payload = {
      patient_id: patientId,
      mode:
        data.scanOption === "upload_new"
          ? ("upload" as const)
          : ("use_last" as const),
      other_details: JSON.stringify({
        archs: data.archRequired,
        notes: data.notes,
        sets: data.sets,
      }),
      stlFile1:
        data.scanOption === "use_last"
          ? getPatientSTLName("upperSTL")
          : data.upperSTL,
      stlFile2:
        data.scanOption === "use_last"
          ? getPatientSTLName("lowerSTL")
          : data.lowerSTL,
      profileRepose:
        submissionPhotos.find((p) => p.name === "profileRepose")?.file ||
        undefined,
      buccalRight:
        submissionPhotos.find((p) => p.name === "buccalRight")?.file ||
        undefined,
      buccalLeft:
        submissionPhotos.find((p) => p.name === "buccalLeft")?.file ||
        undefined,
      frontalRepose:
        submissionPhotos.find((p) => p.name === "frontalRepose")?.file ||
        undefined,
      frontalSmiling:
        submissionPhotos.find((p) => p.name === "frontalSmiling")?.file ||
        undefined,
      labialAnterior:
        submissionPhotos.find((p) => p.name === "labialAnterior")?.file ||
        undefined,
      occlusalLower:
        submissionPhotos.find((p) => p.name === "occlussalLower")?.file ||
        undefined,
      occlusalUpper:
        submissionPhotos.find((p) => p.name === "occlussalUpper")?.file ||
        undefined,
      // Add cbctFile, radioGraph1, radioGraph2 if you have them in your form
    };

    try {
      const token = getDecryptedToken("AccessToken") || "";
      const response = await submitRetainerRequest(token, payload);

      if (response && response.success) {
        toast.success("Retainer request submitted successfully!");
        onClose();
      } else {
        toast.error(response?.message || "Failed to submit retainer request.");
        onClose();
      }
    } catch {
      onClose();
      return null;
    }
  };

  // Prevent form submission when pressing Enter
  const preventEnterSubmit = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && currentStep !== 3) {
      e.preventDefault();
    }
  };

  // Define allowed photo field names
  type PhotoFieldName =
    | "profileRepose"
    | "frontalRepose"
    | "frontalSmiling"
    | "occlussalUpper"
    | "socialSmile"
    | "occlussalLower"
    | "buccalRight"
    | "labialAnterior"
    | "buccalLeft";

  // Helper to get patientData photo URLs with proper typing
  const getPatientPhotoUrl = (
    photoName: PhotoFieldName,
  ): string | StaticImageData => {
    // PatientFileData keys except "socialSmile"
    type PatientPhotoFieldName = Exclude<PhotoFieldName, "socialSmile">;
    // Only allow keys that exist on PatientFileData
    const patientPhotoKeys: PatientPhotoFieldName[] = [
      "profileRepose",
      "frontalRepose",
      "frontalSmiling",
      "occlussalUpper",
      "occlussalLower",
      "buccalRight",
      "labialAnterior",
      "buccalLeft",
    ];
    if (photoName === "socialSmile") {
      // "socialSmile" does not exist in PatientFileData, so always use placeholder
      return photoPlaceholders["socialSmile"];
    }
    if (patientPhotoKeys.includes(photoName as PatientPhotoFieldName)) {
      const photoField = patientData[photoName as PatientPhotoFieldName];
      if (photoField && typeof photoField === "string") {
        return photoField;
      }
    }
    return photoPlaceholders[photoName];
  };
  // Helper to get patientData STL file names
  const getPatientSTLName = (field: "upperSTL" | "lowerSTL"): string | null => {
    if (field === "upperSTL") {
      return patientData?.stlFile1
        ? typeof patientData.stlFile1 === "string"
          ? patientData?.stlFile1
          : patientData.stlFile1
        : null;
    }
    if (field === "lowerSTL") {
      return patientData?.stlFile2
        ? typeof patientData.stlFile2 === "string"
          ? patientData?.stlFile2
          : patientData.stlFile2
        : null;
    }
    return null;
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
      onClick={(e) => e.stopPropagation()}
    >
      <div
        className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            Retainer Request
            {currentStep === 2 && "- Scans"}
            {currentStep === 3 && "- Summary"}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Step indicator */}
        <div className="px-6 pt-6">
          <div className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? "bg-primary text-white" : "bg-gray-200"}`}
            >
              1
            </div>
            <div
              className={`h-1 flex-1 ${currentStep >= 2 ? "bg-primary" : "bg-gray-200"}`}
            ></div>
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? "bg-primary text-white" : "bg-gray-200"}`}
            >
              2
            </div>
            <div
              className={`h-1 flex-1 ${currentStep >= 3 ? "bg-primary" : "bg-gray-200"}`}
            ></div>
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 3 ? "bg-primary text-white" : "bg-gray-200"}`}
            >
              3
            </div>
          </div>
        </div>

        <form
          onSubmit={handleSubmit(onSubmitForm)}
          onKeyDown={preventEnterSubmit}
        >
          {currentStep === 1 ? (
            <div className="p-6 space-y-6">
              {/* Number of sets */}
              <div>
                <label className="block text-lg font-medium text-gray-800 mb-3">
                  Number of sets<span className="text-red-500">*</span>
                </label>
                <div className="flex items-center">
                  <button
                    type="button"
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center"
                    onClick={() => setValue("sets", Math.max(1, sets - 1))}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                  <span className="mx-4 text-lg">{sets} sets</span>
                  <button
                    type="button"
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center"
                    onClick={() => setValue("sets", sets + 1)}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                  <span className="ml-4 text-sm text-gray-500">
                    (Free retainer included, additional retainers require extra
                    fees)
                  </span>
                </div>
                {errors.sets && (
                  <p className="mt-2 text-sm text-red-500">
                    {errors.sets.message}
                  </p>
                )}
              </div>

              <div className="w-full border-t border-gray-200 my-4"></div>

              {/* Arch Required */}
              <div>
                <label className="block text-lg font-medium text-gray-800 mb-3">
                  Arch Required
                </label>
                <div className="flex gap-8">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-5 w-5 accent-orange-600 rounded border-gray-300"
                      value="upper"
                      {...register("archRequired")}
                    />
                    <span className="ml-2 text-gray-700">Upper</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-5 w-5 accent-orange-600 rounded border-gray-300"
                      value="lower"
                      {...register("archRequired")}
                    />
                    <span className="ml-2 text-gray-700">Lower</span>
                  </label>
                </div>
                {errors.archRequired && (
                  <p className="mt-2 text-sm text-red-500">
                    {errors.archRequired.message}
                  </p>
                )}
              </div>

              <div className="w-full border-t border-gray-200 my-4"></div>

              {/* Additional notes */}
              <div>
                <label className="block text-lg font-medium text-gray-800 mb-3">
                  Additional notes:
                </label>
                <textarea
                  className="w-full border border-gray-300 rounded-lg p-3 h-24"
                  placeholder="Type here..."
                  {...register("notes")}
                ></textarea>
              </div>
            </div>
          ) : currentStep === 2 ? (
            <div className="p-6 space-y-6">
              {/* Scans Section */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">
                  Scans
                </h3>
                <div className="flex gap-5 items-center mb-4">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="upload_new"
                      value="upload_new"
                      {...register("scanOption")}
                      className="form-radio h-5 w-5 accent-[#f37021] text-primary border-gray-300"
                    />
                    <label
                      htmlFor="upload_new"
                      className="ml-2 text-gray-700 font-medium"
                    >
                      Upload new scans
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="use_last"
                      value="use_last"
                      {...register("scanOption")}
                      className="form-radio h-5 w-5 accent-[#f37021] text-primary border-gray-300"
                    />
                    <label
                      htmlFor="use_last"
                      className="ml-2 text-gray-700 font-medium"
                    >
                      Use last step
                    </label>
                  </div>
                </div>

                {scanOption === "upload_new" && (
                  <div className="bg-gray-50 p-5 rounded-lg">
                    {/* STL Files Section - MANDATORY */}
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-gray-800 mb-4">
                        STL Files <span className="text-red-500">*</span>
                      </h4>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        {/* Upper STL */}
                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Upper STL <span className="text-red-500">*</span>
                          </label>
                          <div className="flex items-center space-x-2">
                            <input
                              type="file"
                              accept=".zip"
                              onChange={(e) =>
                                handleSTLFileChange(e, "upperSTL")
                              }
                              className="block w-full text-sm text-gray-500
                                                                file:mr-4 file:py-2 file:px-4
                                                                file:rounded-full file:border-0
                                                                file:text-sm file:font-semibold
                                                                file:bg-primary file:text-white
                                                                hover:file:bg-primary-dark"
                            />
                          </div>
                          {upperSTL && (
                            <p className="mt-2 text-xs text-green-600">
                              File selected: {upperSTL.name}
                            </p>
                          )}
                        </div>

                        {/* Lower STL */}
                        <div className="bg-white p-4 rounded-lg border border-gray-200">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Lower STL <span className="text-red-500">*</span>
                          </label>
                          <div className="flex items-center space-x-2">
                            <input
                              type="file"
                              accept=".zip"
                              onChange={(e) =>
                                handleSTLFileChange(e, "lowerSTL")
                              }
                              className="block w-full text-sm text-gray-500
                                                                file:mr-4 file:py-2 file:px-4
                                                                file:rounded-full file:border-0
                                                                file:text-sm file:font-semibold
                                                                file:bg-primary file:text-white
                                                                hover:file:bg-primary-dark"
                            />
                          </div>
                          {lowerSTL && (
                            <p className="mt-2 text-xs text-green-600">
                              File selected: {lowerSTL.name}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Error messages for STL files */}
                      {(errors.upperSTL || errors.lowerSTL) && (
                        <p className="mt-2 text-sm text-red-500 bg-red-50 p-2 rounded">
                          {errors.upperSTL?.message || errors.lowerSTL?.message}
                        </p>
                      )}
                    </div>

                    {/* Photos Section - OPTIONAL */}
                    <div className="mt-8">
                      <h4 className="text-md font-medium text-gray-800 mb-2 flex items-center">
                        Photos{" "}
                        <span className="ml-2 text-sm font-normal text-gray-500">
                          (Optional)
                        </span>
                      </h4>
                      <p className="text-sm text-gray-600 mb-4">
                        Upload patient photos if needed. These help with
                        treatment planning but are not required.
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {photos.map((photo, index) =>
                          photo.name === "socialSmile" ? (
                            <div
                              key={index}
                              className="bg-white rounded-[10px] flex flex-col items-center justify-center p-5 border border-gray-200"
                            >
                              <Image
                                width={150}
                                height={150}
                                src={logo}
                                alt="Logo"
                                className="w-[150px] h-[150px] object-contain"
                              />
                            </div>
                          ) : (
                            <div key={index} className="group relative">
                              <label
                                htmlFor={`upload-${photo.name}`}
                                className={`bg-white relative cursor-pointer rounded-[10px] border border-gray-200 ${photo.file ? "p-0" : "p-4"} flex flex-col items-center justify-center h-[180px]`}
                              >
                                <Image
                                  width={photo.file ? 180 : 120}
                                  height={photo.file ? 180 : 120}
                                  src={getImage(photo)}
                                  alt={photo.name}
                                  className={
                                    photo.file
                                      ? "w-full h-full object-cover rounded-[10px]"
                                      : "w-[120px] h-[120px] object-contain"
                                  }
                                />
                                {photo.file ? (
                                  <button
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleDeletePhoto(photo.name);
                                    }}
                                    type="button"
                                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-4 w-4"
                                      viewBox="0 0 20 20"
                                      fill="currentColor"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  </button>
                                ) : (
                                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-black/30 rounded-[10px] transition-opacity">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-10 w-10 text-white"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 4v16m8-8H4"
                                      />
                                    </svg>
                                  </div>
                                )}
                                <input
                                  id={`upload-${photo.name}`}
                                  type="file"
                                  accept="image/*"
                                  onChange={(e) =>
                                    handlePhotoFileChange(e, photo.name)
                                  }
                                  className="hidden"
                                />
                              </label>
                              <p className="mt-1 text-xs text-gray-500 text-center">
                                {photo.name.replace(/([A-Z])/g, " $1").trim()}
                              </p>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {scanOption === "use_last" && (
                  <div className="bg-gray-50 p-5 rounded-lg">
                    <p className="text-md font-medium text-gray-800 mb-2">
                      Using your last step data
                    </p>
                    <p className="text-sm text-gray-600 mb-4">
                      Your previous data will be used to create your retainers.
                      No need to upload new scans.
                    </p>
                    {/* Show STL file names from patientData */}
                    <div className="bg-white p-4 rounded-lg border border-gray-200 flex items-center justify-between">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Upper STL
                        </label>
                        <p className="mt-2 text-xs text-green-600">
                          {getPatientSTLName("upperSTL") || "No file found"}
                        </p>
                      </div>
                      {getPatientSTLName("upperSTL") && (
                        <button
                          type="button"
                          className="ml-2 text-primary cursor-pointer"
                          onClick={() =>
                            window.open(
                              getPatientSTLName("upperSTL") as string,
                              "_blank",
                            )
                          }
                          title="Open STL file"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="19"
                            viewBox="0 0 18 19"
                            fill="none"
                          >
                            <path
                              d="M8.98535 0.986435C8.72031 0.99032 8.46764 1.09927 8.28288 1.28934C8.09812 1.47941 7.99637 1.73506 8 2.00011V11.586L6.70703 10.2931C6.61373 10.1972 6.50212 10.121 6.37883 10.0689C6.25554 10.0169 6.12307 9.99022 5.98926 9.99034C5.79041 9.99059 5.59615 10.0501 5.43129 10.1613C5.26643 10.2725 5.13846 10.4303 5.06372 10.6146C4.98898 10.7988 4.97088 11.0012 5.01171 11.1958C5.05255 11.3904 5.15047 11.5684 5.29297 11.7071L8.29297 14.7071C8.48051 14.8946 8.73483 14.9999 9 14.9999C9.26517 14.9999 9.51949 14.8946 9.70703 14.7071L12.707 11.7071C12.803 11.615 12.8796 11.5046 12.9324 11.3825C12.9852 11.2604 13.0131 11.1289 13.0144 10.9959C13.0158 10.8629 12.9906 10.7309 12.9403 10.6077C12.89 10.4845 12.8156 10.3726 12.7216 10.2785C12.6275 10.1845 12.5156 10.1101 12.3924 10.0598C12.2692 10.0095 12.1373 9.98431 12.0042 9.98567C11.8712 9.98702 11.7397 10.0149 11.6176 10.0677C11.4955 10.1205 11.3851 10.1971 11.293 10.2931L10 11.586V2.00011C10.0018 1.8664 9.97683 1.73369 9.92647 1.60981C9.87611 1.48594 9.80143 1.37342 9.70683 1.27891C9.61223 1.1844 9.49964 1.10983 9.37572 1.05959C9.25179 1.00935 9.11906 0.984478 8.98535 0.986435ZM3 6.50011C1.35498 6.50011 0 7.85509 0 9.50011V16.0001C0 17.6451 1.35498 19.0001 3 19.0001H15C16.645 19.0001 18 17.6451 18 16.0001V9.50011C18 7.85509 16.645 6.50011 15 6.50011H13.5C13.3675 6.49823 13.2359 6.52271 13.113 6.57213C12.99 6.62154 12.8781 6.6949 12.7837 6.78795C12.6893 6.88099 12.6144 6.99186 12.5632 7.11412C12.5121 7.23638 12.4858 7.36758 12.4858 7.50011C12.4858 7.63263 12.5121 7.76384 12.5632 7.88609C12.6144 8.00835 12.6893 8.11922 12.7837 8.21227C12.8781 8.30531 12.99 8.37867 13.113 8.42809C13.2359 8.4775 13.3675 8.50198 13.5 8.50011H15C15.564 8.50011 16 8.93612 16 9.50011V16.0001C16 16.5641 15.564 17.0001 15 17.0001H3C2.43602 17.0001 2 16.5641 2 16.0001V9.50011C2 8.93612 2.43602 8.50011 3 8.50011H4.5C4.63251 8.50198 4.76407 8.4775 4.88704 8.42809C5.01001 8.37867 5.12193 8.30531 5.2163 8.21227C5.31067 8.11922 5.38561 8.00835 5.43676 7.88609C5.4879 7.76384 5.51424 7.63263 5.51424 7.50011C5.51424 7.36758 5.4879 7.23638 5.43676 7.11412C5.38561 6.99186 5.31067 6.88099 5.2163 6.78795C5.12193 6.6949 5.01001 6.62154 4.88704 6.57213C4.76407 6.52271 4.63251 6.49823 4.5 6.50011H3Z"
                              fill="#000"
                            />
                          </svg>
                          {/* <FaExternalLinkAlt size={18} /> */}
                        </button>
                      )}
                    </div>

                    <div className="bg-white p-4 rounded-lg border border-gray-200 flex items-center justify-between">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Lower STL
                        </label>
                        <p className="mt-2 text-xs text-green-600">
                          {getPatientSTLName("lowerSTL") || "No file found"}
                        </p>
                      </div>
                      {getPatientSTLName("lowerSTL") && (
                        <button
                          type="button"
                          className="ml-2 text-primary cursor-pointer"
                          onClick={() =>
                            window.open(
                              getPatientSTLName("lowerSTL") as string,
                              "_blank",
                            )
                          }
                          title="Open STL file"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="18"
                            height="19"
                            viewBox="0 0 18 19"
                            fill="none"
                          >
                            <path
                              d="M8.98535 0.986435C8.72031 0.99032 8.46764 1.09927 8.28288 1.28934C8.09812 1.47941 7.99637 1.73506 8 2.00011V11.586L6.70703 10.2931C6.61373 10.1972 6.50212 10.121 6.37883 10.0689C6.25554 10.0169 6.12307 9.99022 5.98926 9.99034C5.79041 9.99059 5.59615 10.0501 5.43129 10.1613C5.26643 10.2725 5.13846 10.4303 5.06372 10.6146C4.98898 10.7988 4.97088 11.0012 5.01171 11.1958C5.05255 11.3904 5.15047 11.5684 5.29297 11.7071L8.29297 14.7071C8.48051 14.8946 8.73483 14.9999 9 14.9999C9.26517 14.9999 9.51949 14.8946 9.70703 14.7071L12.707 11.7071C12.803 11.615 12.8796 11.5046 12.9324 11.3825C12.9852 11.2604 13.0131 11.1289 13.0144 10.9959C13.0158 10.8629 12.9906 10.7309 12.9403 10.6077C12.89 10.4845 12.8156 10.3726 12.7216 10.2785C12.6275 10.1845 12.5156 10.1101 12.3924 10.0598C12.2692 10.0095 12.1373 9.98431 12.0042 9.98567C11.8712 9.98702 11.7397 10.0149 11.6176 10.0677C11.4955 10.1205 11.3851 10.1971 11.293 10.2931L10 11.586V2.00011C10.0018 1.8664 9.97683 1.73369 9.92647 1.60981C9.87611 1.48594 9.80143 1.37342 9.70683 1.27891C9.61223 1.1844 9.49964 1.10983 9.37572 1.05959C9.25179 1.00935 9.11906 0.984478 8.98535 0.986435ZM3 6.50011C1.35498 6.50011 0 7.85509 0 9.50011V16.0001C0 17.6451 1.35498 19.0001 3 19.0001H15C16.645 19.0001 18 17.6451 18 16.0001V9.50011C18 7.85509 16.645 6.50011 15 6.50011H13.5C13.3675 6.49823 13.2359 6.52271 13.113 6.57213C12.99 6.62154 12.8781 6.6949 12.7837 6.78795C12.6893 6.88099 12.6144 6.99186 12.5632 7.11412C12.5121 7.23638 12.4858 7.36758 12.4858 7.50011C12.4858 7.63263 12.5121 7.76384 12.5632 7.88609C12.6144 8.00835 12.6893 8.11922 12.7837 8.21227C12.8781 8.30531 12.99 8.37867 13.113 8.42809C13.2359 8.4775 13.3675 8.50198 13.5 8.50011H15C15.564 8.50011 16 8.93612 16 9.50011V16.0001C16 16.5641 15.564 17.0001 15 17.0001H3C2.43602 17.0001 2 16.5641 2 16.0001V9.50011C2 8.93612 2.43602 8.50011 3 8.50011H4.5C4.63251 8.50198 4.76407 8.4775 4.88704 8.42809C5.01001 8.37867 5.12193 8.30531 5.2163 8.21227C5.31067 8.11922 5.38561 8.00835 5.43676 7.88609C5.4879 7.76384 5.51424 7.63263 5.51424 7.50011C5.51424 7.36758 5.4879 7.23638 5.43676 7.11412C5.38561 6.99186 5.31067 6.88099 5.2163 6.78795C5.12193 6.6949 5.01001 6.62154 4.88704 6.57213C4.76407 6.52271 4.63251 6.49823 4.5 6.50011H3Z"
                              fill="#000"
                            />
                          </svg>
                          {/* <FaExternalLinkAlt size={18} /> */}
                        </button>
                      )}
                    </div>
                    {/* Show patientData photos */}
                    <div className="mt-8">
                      <h4 className="text-md font-medium text-gray-800 mb-2 flex items-center">
                        Photos from last step
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {photos.map((photo, index) =>
                          photo.name === "socialSmile" ? (
                            <div
                              key={index}
                              className="bg-white rounded-[10px] flex flex-col items-center justify-center p-5 border border-gray-200"
                            >
                              <Image
                                width={150}
                                height={150}
                                src={logo}
                                alt="Logo"
                                className="w-[150px] h-[150px] object-contain"
                              />
                            </div>
                          ) : (
                            <div key={index} className="group relative">
                              <div
                                className={`bg-white relative rounded-[10px] border border-gray-200 p-4 flex flex-col items-center justify-center h-[180px]`}
                              >
                                <Image
                                  width={180}
                                  height={180}
                                  src={getPatientPhotoUrl(
                                    photo.name as PhotoFieldName,
                                  )}
                                  alt={photo.name}
                                  className="w-full h-full object-cover rounded-[10px]"
                                />
                              </div>
                              <p className="mt-1 text-xs text-gray-500 text-center">
                                {photo.name.replace(/([A-Z])/g, " $1").trim()}
                              </p>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            // STEP 3: SUMMARY
            <div className="p-6 space-y-6">
              <div className="bg-white p-5 rounded-lg border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Request Summary
                </h3>

                {/* Basic Details */}
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">
                    Basic Details
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Number of Sets</p>
                      <p className="font-medium">
                        {sets} {sets === 1 ? "set" : "sets"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Arch Required</p>
                      <p className="font-medium">
                        {archRequired.includes("upper") &&
                          archRequired.includes("lower")
                          ? "Upper and Lower"
                          : archRequired.includes("upper")
                            ? "Upper"
                            : "Lower"}
                      </p>
                    </div>
                  </div>

                  {notes && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-500">Additional Notes</p>
                      <p className="mt-1 text-sm bg-gray-50 p-2 rounded">
                        {notes}
                      </p>
                    </div>
                  )}
                </div>

                {/* Scan Information */}
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">
                    Scan Information
                  </h4>
                  <div>
                    <p className="text-sm text-gray-500">Scan Option</p>
                    <p className="font-medium">
                      {scanOption === "upload_new"
                        ? "Upload new scans"
                        : "Use last step data"}
                    </p>
                  </div>

                  {scanOption === "upload_new" && (
                    <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                      {upperSTL && (
                        <div>
                          <p className="text-sm text-gray-500">Upper STL</p>
                          {/* <p className="font-medium text-sm text-green-600">{upperSTL.name}</p> */}
                        </div>
                      )}

                      {lowerSTL && (
                        <div>
                          <p className="text-sm text-gray-500">Lower STL</p>
                          {/* <p className="font-medium text-sm text-green-600">{lowerSTL.name}</p> */}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Photos */}
                {photos.some((photo) => photo.file !== null) && (
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">
                      Photos
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                      {photos
                        .filter(
                          (photo) =>
                            photo.file !== null && photo.name !== "socialSmile",
                        )
                        .map((photo, idx) => (
                          <div key={idx} className="text-center">
                            <div className="bg-gray-100 rounded-md overflow-hidden h-[120px] w-full">
                              <Image
                                width={1000}
                                height={1000}
                                src={photoUrls[photo.name]}
                                alt={formatPhotoName(photo.name)}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="border-t border-gray-200 p-6 flex justify-between">
            {currentStep === 1 ? (
              <div />
            ) : (
              <button
                type="button"
                onClick={handleBack}
                className="px-6 py-1.5 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 cursor-pointer"
              >
                Back
              </button>
            )}

            <div className="flex gap-4">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-1.5 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 cursor-pointer"
              >
                Cancel
              </button>

              {currentStep < 3 ? (
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="px-6 py-1.5 bg-primary text-white rounded-full font-medium hover:bg-primary-dark cursor-pointer"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  className="px-6 py-1.5 bg-primary text-white rounded-full font-medium cursor-pointer hover:bg-primary-dark"
                >
                  Submit Request
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RetainerRequestModal;
