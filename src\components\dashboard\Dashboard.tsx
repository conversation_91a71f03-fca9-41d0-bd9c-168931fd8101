"use client";

import Header from "../reuseable/Header";
import TableComponent from "../reuseable/ReuseableTable";
import { useState, useEffect } from "react";
import plusIcon from "../../../public/svgs/plus-sign.svg";
import { useRouter } from "next/navigation";
import { columns } from "../../constants/constants";
import { PatientData } from "@/types/types";
import { API_ROUTES } from "@/utils/ApiRoutes";
import { fetchPaginatedData } from "@/utils/ApisHelperFunction";
import { getDecryptedToken } from "@/app/lib/auth";
import Image from "next/image";
import { toast } from "react-toastify";

interface PatientsApiResponse {
  status: number;
  success: boolean;
  data: {
    data: PatientData[];
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      total: number;
    };
  };
  message: string;
}

interface TransformedPatientData {
  id: string;
  patient: {
    image?: string;
    name: string;
    id: string;
  };
  startDate: string;
  treatmentOption: string;
  status: string;
  notes: string;
  daysSinceLastUpdate: number;
  country: string;
}

type TabType = "treatment" | "action" | "archived";

const Dashboard = () => {
  const router = useRouter();
  const token = getDecryptedToken("AccessToken") || "";
  const Role = getDecryptedToken("Role");

  const [allPatients, setAllPatients] = useState<PatientData[]>([]);
  const [filteredData, setFilteredData] = useState<TransformedPatientData[]>([]);
  const [loading, setLoading] = useState(false);

  // Pagination state per tab
  const [pagination, setPagination] = useState<Record<
    TabType,
    {
      page: number;
      limit: number;
      totalPages: number;
      totalItems: number;
    }
  >>({
    treatment: { page: 1, limit: 50, totalPages: 0, totalItems: 0 },
    action: { page: 1, limit: 50, totalPages: 0, totalItems: 0 },
    archived: { page: 1, limit: 50, totalPages: 0, totalItems: 0 },
  });

  const [activeTab, setActiveTab] = useState<TabType>("treatment");
  const [searchQuery, setSearchQuery] = useState("");

  // Transform patient data for table
  const transformPatientDataToTableRow = (patientData: PatientData[]) => {
    return patientData.map((patient) => ({
      id: patient?.id?.toString(),
      patient: {
        image: patient?.profileRepose || undefined,
        name: `${patient?.first_name} ${patient?.last_name}`,
        id: patient?.uuid ? `#${patient?.uuid}` : "#",
      },
      startDate: new Date(patient?.created_at).toLocaleDateString(),
      treatmentOption: patient?.plan_name || "N/A",
      status: patient?.status || "Not Started Yet",
      notes: patient?.general_notes || "No notes",
      daysSinceLastUpdate: Math.floor(
        (new Date().getTime() - new Date(patient?.updated_at).getTime()) /
          (1000 * 60 * 60 * 24)
      ),
      country: patient?.country || "Not available",
    }));
  };

  // Fetch all patients (first page only, as backend paginates)
  const fetchPatients = async () => {
    setLoading(true);
    try {
      const endpoint =
        Role === "specialist"
          ? API_ROUTES.PATIENT.GET_ALL_PATIENTS_FOR_SPECIALIST
          : API_ROUTES.PATIENT.GET_ALL_PATIENTS;

      // Fetch first 1000 patients at once or handle multiple pages if needed
      // WARNING: This might not scale well if data is huge, so consider backend support!
      const response = await fetchPaginatedData<PatientsApiResponse>(
        `${endpoint}?page=1&limit=1000`,
        1,
        1000,
        token
      );

      if (response?.data) {
        setAllPatients(response.data.data);
      }
    } catch {
      toast.error("Failed to fetch patients");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPatients();
  }, []);
  useEffect(() => {
  if (allPatients.length === 0) return;

  const getCountByTab = (tab: TabType, data: PatientData[]) => {
    switch (tab) {
      case "treatment":
         return data.filter((p) => p.is_active !== true && p?.status !== null && p?.status !== "archived").length;  
      case "action":
        return data.filter((p) => p?.status === null).length;
      case "archived":
        return data.filter((p) => p?.status === "archived" || !p?.is_active).length;
      default:
        return data.length;
    }
  };

  setPagination((prev) => ({
    treatment: { ...prev.treatment, totalItems: getCountByTab("treatment", allPatients), totalPages: Math.ceil(getCountByTab("treatment", allPatients) / prev.treatment.limit) || 1 },
    action: { ...prev.action, totalItems: getCountByTab("action", allPatients), totalPages: Math.ceil(getCountByTab("action", allPatients) / prev.action.limit) || 1 },
    archived: { ...prev.archived, totalItems: getCountByTab("archived", allPatients), totalPages: Math.ceil(getCountByTab("archived", allPatients) / prev.archived.limit) || 1 },
  }));
}, [allPatients]);

  // Filter by tab & search, then paginate locally
  useEffect(() => {
    // Filter patients based on active tab
    const getDataByTab = (tab: TabType, data: PatientData[]): PatientData[] => {
  switch (tab) {
    case "treatment":
      return data.filter(
        (p) => p.is_active !== false && p?.status !== null && p?.status !== "archived"
      );
    case "action":
      return data.filter((p) => p?.status === null);
    case "archived":
      return data.filter((p) => p?.status === "archived" || !p?.is_active);
    default:
      return data;
  }
};


    // Filter by tab first
    let filtered = getDataByTab(activeTab, allPatients);

    // Filter by search query if any
    if (searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((patient) => {
        const fullName = `${patient?.first_name} ${patient?.last_name}`.toLowerCase();
        return (
          fullName.includes(searchTerm) ||
          patient?.id.toString().includes(searchTerm) ||
          patient?.uuid?.toLowerCase().includes(searchTerm) ||
          patient?.email?.toLowerCase().includes(searchTerm) ||
          patient?.plan_name?.toLowerCase().includes(searchTerm) ||
          patient?.status?.toLowerCase().includes(searchTerm) ||
          patient?.general_notes?.toLowerCase().includes(searchTerm) ||
          patient?.country?.toLowerCase().includes(searchTerm)
        );
      });
    }

    // Update pagination totals for this tab
    const totalItems = filtered.length;
    const { page, limit } = pagination[activeTab];
    const totalPages = Math.ceil(totalItems / limit) || 1;

    // Adjust current page if it's out of range after filtering
    const currentPage = page > totalPages ? totalPages : page;

    // Slice the filtered data for current page
    const pagedData = filtered.slice(
      (currentPage - 1) * limit,
      currentPage * limit
    );

    // Update filtered data and pagination state for the active tab
    setFilteredData(transformPatientDataToTableRow(pagedData));
    setPagination((prev) => ({
      ...prev,
      [activeTab]: {
        page: currentPage,
        limit,
        totalPages,
        totalItems,
      },
    }));
  }, [allPatients, activeTab, searchQuery, pagination[activeTab].page, pagination[activeTab].limit]);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchQuery("");
    // Reset page to 1 on tab change
    setPagination((prev) => ({
      ...prev,
      [tab]: { ...prev[tab], page: 1 },
    }));
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    // Reset page to 1 on search
    setPagination((prev) => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], page: 1 },
    }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], page: newPage },
    }));
  };

  const handleItemsPerPageChange = (newLimit: number) => {
    setPagination((prev) => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], limit: newLimit, page: 1 },
    }));
  };

  const tabs = [
    {
      value: "treatment",
      label: "In Treatment",
      count: pagination.treatment.totalItems,
    },
    {
      value: "action",
      label: "Action Required",
      count: pagination.action.totalItems,
    },
    {
      value: "archived",
      label: "Archived",
      count: pagination.archived.totalItems,
    },
  ];

  return (
    <div className="bg-transparent px-4 py-2">
      <Header onSearchChange={handleSearchChange} searchValue={searchQuery} />

      <div className="rounded-lg pt-6">
        <div className="flex lg:flex-row flex-col items-center lg:justify-between mb-4">
          {Role !== "specialist" && (
            <div className="flex space-x-3">
              {tabs.map((tab) => (
                <button
                  key={tab.value}
                  type="button"
                  onClick={() => handleTabChange(tab.value as TabType)}
                  className={`rounded-full px-4 py-1.5 transition-all cursor-pointer duration-200 font-medium ${
                    activeTab === tab.value
                      ? "bg-[#EB6309] text-white hover:bg-[#D45A08]"
                      : "bg-white text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  {tab.label} ({tab.count > 99 ? "99+" : tab.count})
                </button>
              ))}
            </div>
          )}

          <div className="flex lg:justify-start justify-end gap-2 lg:mb-0 mb-2">
            {Role !== "specialist" && (
              <>
                <button
                  onClick={() => router.push("/patient-data")}
                  className="rounded-full cursor-pointer flex gap-1 items-center px-4 py-1.5 transition-all duration-200 font-medium bg-[#EB6309] text-white hover:bg-[#D45A08]"
                >
                  <span>Add New Patient</span>
                  <Image
                    src={plusIcon}
                    width={1000}
                    height={1000}
                    alt="Plus Icon"
                    className="w-5 h-5"
                  />
                </button>
                <button
                  onClick={() => router.push("/patient-retainer")}
                  className="rounded-full cursor-pointer flex gap-1 items-center px-4 py-1.5 transition-all duration-200 font-medium bg-[#EB6309] text-white hover:bg-[#D45A08]"
                >
                  <span>Add New Retainer</span>
                  <Image
                    src={plusIcon}
                    width={1000}
                    height={1000}
                    alt="Plus Icon"
                    className="w-5 h-5"
                  />
                </button>
              </>
            )}
          </div>
        </div>

        <div className="tab-content">
          {loading ? (
            <div className="text-center py-4">Loading...</div>
          ) : filteredData.length > 0 ? (
            <TableComponent
              columns={columns}
              data={filteredData}
              pagination={{
                currentPage: pagination[activeTab].page,
                totalItems: pagination[activeTab].totalItems,
                itemsPerPage: pagination[activeTab].limit,
                totalPages: pagination[activeTab].totalPages,
                onPageChange: handlePageChange,
                onItemsPerPageChange: handleItemsPerPageChange,
              }}
            />
          ) : (
            <div className="text-center text-gray-500 py-4">No results found</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
