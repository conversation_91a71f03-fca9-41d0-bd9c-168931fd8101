import { fetchApi, getServerCookies } from "@/api/getapis";
import MainSummary from "@/components/summary/Summary";
import { PatientFileData } from "@/types/types";
import { API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import React from "react";
export const dynamic = "force-dynamic";

const page = async () => {
    const patientId = await getServerCookies("patientId");
    let patientData: PatientFileData | null = null;
  
    if (patientId) {
      // Get role to determine correct API endpoint
      const role = await getServerCookies("Role");
      const baseRoute =
        role === "specialist"
          ? API_SERVER_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST
          : API_SERVER_ROUTES.PATIENT.GET_PATIENT_BY_ID;
  
      const patientsArray = await fetchApi(`${baseRoute}/${patientId}`);
      patientData = patientsArray as PatientFileData;
      // console.log("🚀 ~ page ~ patientData:", patientData);
    } else {
      console.log("🚀 ~ page ~ patientData:", patientData);
    }
  return <MainSummary patientData={patientData} />;
};

export default page;
