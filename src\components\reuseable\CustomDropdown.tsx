// components/CustomDropdown.tsx
"use client";

import React, { useState, useEffect, useRef } from "react";

interface Option {
  value: string;
  label: string;
}

interface CustomDropdownProps {
  name: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  disabled?: boolean;
  error?: string;
}

export default function CustomDropdown({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  name,
  value,
  onChange,
  options,
  placeholder = "Choose an option",
  disabled,
  error,
}: CustomDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const rootRef = useRef<HTMLDivElement>(null);

  const selectedLabel =
    options.find((o) => o.value === value)?.label || placeholder;

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (rootRef.current && !rootRef.current.contains(e.target as Node)) {
        setIsOpen(false);
      }
    };
    if (isOpen) document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen]);

  return (
    <div className="relative w-full" ref={rootRef}>
      <button
        type="button"
        disabled={disabled}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={`w-full flex items-center justify-between rounded-md border px-3 py-2 text-left text-base focus:outline-orange focus:ring-2 focus:ring-primary
          ${error ? "border-red-500" : "border-gray-300"}
          ${disabled ? "cursor-not-allowed bg-gray-100" : "bg-white"}`}
      >
        <span>{selectedLabel}</span>
       
      </button>

      {isOpen && !disabled && (
        <ul className="absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg">
          {options.map((opt) => (
            <li
              key={opt.value}
              onClick={() => {
                onChange(opt.value);
                setIsOpen(false);
              }}
              className={`cursor-pointer px-3 py-2 text-sm hover:bg-primary hover:text-white
                ${value === opt.value ? "bg-primary/10 font-medium" : ""}`}
            >
              {opt.label}
            </li>
          ))}
        </ul>
      )}

      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
}