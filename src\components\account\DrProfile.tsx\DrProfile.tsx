"use client"

import type React from "react"
import { useState } from "react"
import { toast } from "react-toastify"
import type { Address, ProfileData } from "@/types/types"
import { getDecryptedToken, setEncryptedToken } from "@/app/lib/auth" // Added setEncryptedToken import
import { addAddress, deleteAddress, updateAddress } from "@/api/account/addresses"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { updateDoctorProfile } from "@/api/account/profile" // Add this import
import { changePassword } from "@/utils/ApisHelperFunction"
import AddressModal from "./modals/AddressModal"
import DeleteConfirmationModal from "@/components/reuseable/DeleteConfirmationModal"
import HidePassword from "@/components/reuseable/Icons/HidePassword"
import ShowPassword from "@/components/reuseable/Icons/ShowPassword"



type ModalAddress = {
  id?: string | number
  clinic_name: string
  street_address: string
  city: string
  postal_code: string
  phone_number: string
  address_type: "ship_to" | "bill_to"
}

const initialModalAddress: ModalAddress = {
  id: "",
  clinic_name: "",
  street_address: "",
  city: "",
  postal_code: "",
  phone_number: "",
  address_type: "ship_to",
}

interface DrProfileProps {
  data: Address[]
  Profiledata: ProfileData
}

const validatePassword = (password: string) => {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/[0-9]/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    errors.push("Password must contain at least one special character")
  }

  return errors
}

const DrProfile: React.FC<DrProfileProps> = ({ data, Profiledata }) => {
  // Split addresses by type directly from props
  const shippingAddresses = data.filter((addr) => addr.address_type === "ship_to")
  const billingAddresses = data.filter((addr) => addr.address_type === "bill_to")

  const [username, setUsername] = useState(Profiledata?.username || "")
  const [firstName, setFirstName] = useState(Profiledata?.first_name || "")
  const [lastName, setLastName] = useState(Profiledata?.last_name || "")
  const [email, setEmail] = useState(Profiledata?.email || "")
  const [currentProfileImage, setCurrentProfileImage] = useState(Profiledata?.profile_image || "")

  // Profile fields from props
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>("")

  const [isEditMode, setIsEditMode] = useState(false)
  const [editUsername, setEditUsername] = useState(Profiledata?.username || "")
  const [editFirstName, setEditFirstName] = useState(Profiledata?.first_name || "")
  const [editLastName, setEditLastName] = useState(Profiledata?.last_name || "")
  const [editEmail, setEditEmail] = useState(Profiledata?.email || "")

  const [updateLoading, setUpdateLoading] = useState(false)
  const [password] = useState("*************") // Added password state variable to fix TypeScript error

  // Notification preferences


  // Address Modal State
  const [addressModalOpen, setAddressModalOpen] = useState(false)
  const [addressModalType, setAddressModalType] = useState<"add" | "edit" | null>(null)
  const [addressType, setAddressType] = useState<"ship_to" | "bill_to">("ship_to")
  const [addressModalData, setAddressModalData] = useState<ModalAddress>({ ...initialModalAddress })

  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState<{ type: "shipping" | "billing"; id: string } | null>(null)

  // Default address IDs
  const [defaultShippingId, setDefaultShippingId] = useState<number | null>(shippingAddresses[0]?.id ?? null)
  const [defaultBillingId, setDefaultBillingId] = useState<number | null>(billingAddresses[0]?.id ?? null)
  const [isEditingPassword, setIsEditingPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  const [newPasswordErrors, setNewPasswordErrors] = useState<string[]>([])
  const [confirmPasswordErrors, setConfirmPasswordErrors] = useState<string[]>([])
  const [passwordsMatch, setPasswordsMatch] = useState(true)
  const [passwordSameAsOld, setPasswordSameAsOld] = useState(false)
  const [wrongCurrentError, setWrongCurrentError] = useState("");
  const [showCurr, setShowCurr] = useState(false);
  const [showNew, setShowNew] = useState(false);
  const [showConf, setShowConf] = useState(false);
  const router = useRouter()

  const handleEditMode = () => {
    if (isEditMode) {
      // Cancel edit mode - revert changes
      setEditUsername(username)
      setEditFirstName(firstName)
      setEditLastName(lastName)
      setEditEmail(email)
      setSelectedImage(null)
      setImagePreview("")
      setCurrentProfileImage(Profiledata?.profile_image || "")
      const fileInput = document.getElementById("profile-image") as HTMLInputElement
      if (fileInput) fileInput.value = ""
    } else {
      // Enter edit mode - set edit values to current values
      setEditUsername(username)
      setEditFirstName(firstName)
      setEditLastName(lastName)
      setEditEmail(email)
    }
    setIsEditMode(!isEditMode)
  }

  const handleSaveProfile = async () => {
    if (!editFirstName.trim() || !editLastName.trim() || !editUsername.trim() || !editEmail.trim()) {
      toast.error("All fields are required.")
      return
    }

    setUpdateLoading(true)
    try {
      const token = getDecryptedToken("AccessToken") || ""
      const payload = {
        first_name: editFirstName,
        last_name: editLastName,
        username: editUsername,
        email: editEmail,
        profileImage: selectedImage ?? null,
      }

      const response = await updateDoctorProfile(token, payload)

      if (response) {
        console.log("Update successful. Full response:", response)

        if (response.data) {
          console.log("Profile was updated successfully:", response.data)

          // ✅ Use backend-provided image if available
          if (response.data.profile_image) {
            setEncryptedToken("profile_image", response.data.profile_image)
            setCurrentProfileImage(response.data.profile_image)
          } else {
            // ✅ fallback to existing one if backend didn’t return
            setEncryptedToken("profile_image", currentProfileImage)
            setCurrentProfileImage(currentProfileImage)
          }
        } else {
          console.log("Update failed, no profile data returned. Message:", response.message)
        }
      } else {
        console.log("Update failed — no response from server.")
      }

      // ✅ Update text fields in local state
      setFirstName(editFirstName)
      setLastName(editLastName)
      setUsername(editUsername)
      setEmail(editEmail)

      setEncryptedToken("first_name", editFirstName)
      setEncryptedToken("last_name", editLastName)
      setEncryptedToken("username", editUsername)

      // ✅ Don’t overwrite backend URL with blob anymore
      // Only use blob for temporary preview while editing
      if (selectedImage) {
        setImagePreview(URL.createObjectURL(selectedImage))
      }

      // Dispatch update event
      const profileUpdateEvent = new CustomEvent("profileUpdated", {
        detail: {
          first_name: editFirstName,
          last_name: editLastName,
          username: editUsername,
          email: editEmail,
          profile_image: response?.data?.profile_image || currentProfileImage,
        },
      })
      window.dispatchEvent(profileUpdateEvent)

      setIsEditMode(false)
      setSelectedImage(null)
      setImagePreview("")
      router.refresh()
    } catch {
      toast.error("Profile update failed!")
    }
    setUpdateLoading(false)
  }



  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
      if (!validTypes.includes(file.type)) {
        toast.error("Please select a valid image file (JPEG, PNG, GIF, WebP)")
        return
      }
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB")
        return
      }
      setSelectedImage(file)
      const reader = new FileReader()
      reader.onload = (event) => {
        const newImageUrl = event.target?.result as string
        setImagePreview(newImageUrl)
        setCurrentProfileImage(newImageUrl)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setSelectedImage(null)
    setImagePreview("")
    setCurrentProfileImage(Profiledata?.profile_image || "")
    const fileInput = document.getElementById("profile-image") as HTMLInputElement
    if (fileInput) fileInput.value = ""
  }



  // Profile update handler (API call should be implemented)
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    await handleSaveProfile()
  }

  // Address management handlers
  const handleAddAddress = (type: "ship_to" | "bill_to") => {
    setAddressModalType("add")
    setAddressType(type)
    setAddressModalData({ ...initialModalAddress, address_type: type })
    setAddressModalOpen(true)
  }

  const handleEditAddress = (addr: Address) => {
    setAddressModalType("edit")
    setAddressType(addr.address_type as "ship_to" | "bill_to")
    setAddressModalData({
      id: addr.id,
      clinic_name: addr.clinic_name,
      street_address: addr.street_address,
      city: addr.city,
      postal_code: addr.postal_code,
      phone_number: addr.phone_number,
      address_type: addr.address_type as "ship_to" | "bill_to",
    })
    setAddressModalOpen(true)
  }

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddressModalData({
      ...addressModalData,
      [e.target.name]: e.target.value,
    })
  }

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const token = getDecryptedToken("AccessToken") || ""
    const { id, clinic_name, street_address, city, postal_code, phone_number } = addressModalData

    if (addressModalType === "add") {
      await addAddress({
        clinic_name,
        street_address,
        city,
        postal_code,
        phone_number,
        address_type: addressType,
        token,
      })
    } else if (addressModalType === "edit") {
      await updateAddress({
        addressId: id ?? "",
        clinic_name,
        street_address,
        city,
        postal_code,
        phone_number,
        address_type: addressType,
        token,
      })
    }
    setAddressModalOpen(false)
    router.refresh()
  }

  const setDefaultAddress = (id: number, type: "ship_to" | "bill_to") => {
    if (type === "ship_to") setDefaultShippingId(id)
    else setDefaultBillingId(id)
  }

  const handleDeleteAddress = (type: "shipping" | "billing", id: number) => {
    setAddressToDelete({ type, id: id.toString() })
    setConfirmModalOpen(true)
  }

  const confirmDeleteAddress = async () => {
    if (!addressToDelete) return
    const { id } = addressToDelete
    const token = getDecryptedToken("AccessToken")
    await deleteAddress({ addressId: id, token: token || "" })
    setConfirmModalOpen(false)
    setAddressToDelete(null)
    router.refresh()
  }

  const cancelDeleteAddress = () => {
    setConfirmModalOpen(false)
    setAddressToDelete(null)
  }

  const handleCancelPasswordEdit = () => {
    setCurrentPassword("")
    setNewPassword("")
    setConfirmPassword("")
    setNewPasswordErrors([])
    setConfirmPasswordErrors([])
    setPasswordsMatch(true)
    setPasswordSameAsOld(false)
    setIsEditingPassword(false)
  }

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setNewPassword(password)
    setNewPasswordErrors(validatePassword(password))

    setPasswordSameAsOld(Boolean(currentPassword && password === currentPassword))

    if (confirmPassword) {
      setPasswordsMatch(password === confirmPassword)
    }
  }

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setConfirmPassword(password)

    setPasswordsMatch(newPassword === password)
  }

  const handleChangePassword = async () => {
    if (isEditingPassword) {
      setWrongCurrentError("");
      if (!currentPassword.trim() || !newPassword.trim() || !confirmPassword.trim()) {
        toast.error("All password fields are required.")
        return
      }

      if (passwordSameAsOld) {
        return
      }

      if (newPasswordErrors.length > 0 || confirmPasswordErrors.length > 0) {
        toast.error("Please fix password validation errors before submitting.")
        return
      }

      if (!passwordsMatch) {
        toast.error("New password and confirm password do not match.")
        return
      }

      const success = await changePassword(currentPassword, newPassword, confirmPassword)

      if (success) {
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")
        setNewPasswordErrors([])
        setConfirmPasswordErrors([])
        setPasswordsMatch(true)
        setPasswordSameAsOld(false)
        setIsEditingPassword(false)
      }
      else {
        setWrongCurrentError("Current password is incorrect");
      }
    } else {
      setIsEditingPassword(true)
    }
  }

  return (
    <>
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-2xl font-bold text-gray-800 mb-8">Profile</h2>
        <form onSubmit={handleUpdateProfile} className="grid grid-cols-1 md:grid-cols-2 gap-8">

          {/* User Information Section */}
          <div className="space-y-6">


            {/* Profile Image Section */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700 mb-2">Profile Image</p>
                <div className="flex items-center justify-between">
                  <div className="w-20 h-20 rounded-full border-2 border-gray-300 overflow-hidden bg-gray-100 flex items-center justify-center">
                    {currentProfileImage ? (
                      <Image
                        src={currentProfileImage || "/placeholder.svg"}
                        alt="Profile"
                        width={1000}
                        height={1000}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    )}
                  </div>
                  {isEditMode && (
                    <div className="flex items-center gap-2 ">
                      <label
                        htmlFor="profile-image"
                        className="bg-[#EB6309] margin-[0px] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm text-center"
                      >
                        {currentProfileImage ? "Change Image" : "Upload Image"}
                      </label>
                      <input
                        id="profile-image"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      {(imagePreview || selectedImage) && (
                        <button
                          type="button"
                          onClick={removeImage}
                          className="text-red-500 text-sm hover:text-red-700 transition-colors"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* First Name */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">First Name</p>
                {isEditMode ? (
                  <input
                    type="text"
                    value={editFirstName}
                    onChange={(e) => setEditFirstName(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter first name"
                    title="First Name"
                  />
                ) : (
                  <p className="text-gray-600">{firstName}</p>
                )}
              </div>
            </div>

            {/* Last Name */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Last Name</p>
                {isEditMode ? (
                  <input
                    type="text"
                    value={editLastName}
                    onChange={(e) => setEditLastName(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter last name"
                    title="Last Name"
                  />
                ) : (
                  <p className="text-gray-600">{lastName}</p>
                )}
              </div>
            </div>

            {/* Username */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Username</p>
                {isEditMode ? (
                  <input
                    type="text"
                    value={editUsername}
                    onChange={(e) => setEditUsername(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter username"
                    title="Username"
                  />
                ) : (
                  <p className="text-gray-600">{username}</p>
                )}
              </div>
            </div>

            {/* Primary Account Email */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Primary Account Email*</p>
                {isEditMode ? (
                  <input
                    type="email"
                    value={editEmail}
                    onChange={(e) => setEditEmail(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter email"
                    title="Email"
                  />
                ) : (
                  <p className="text-gray-600">{email}</p>
                )}
              </div>
            </div>

            {/* Password - Keep independent password section */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Password</p>
                {isEditingPassword ? (
                  <div className="space-y-3 mt-2">
                    <div className="relative">
                    <input
                      type={showCurr ? "text" : "password"}
                      placeholder="Current Password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />
                    <button
                      type="button"
                      onClick={() => setShowCurr(!showCurr)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer hover:opacity-75 transition-opacity"
                      aria-label="Toggle current password visibility"
                    >
                      {showCurr ? <HidePassword /> : <ShowPassword />}
                    </button>
                    </div>
                    {wrongCurrentError && (
                      <p className="text-red-500 text-xs mt-1">{wrongCurrentError}</p>
                    )}
                
                    <div className="relative">
                      <input
                        type={showNew ? "text" : "password"}
                        placeholder="New Password"
                        value={newPassword}
                        onChange={handleNewPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${newPasswordErrors.length > 0 || passwordSameAsOld ? "border-red-300" : "border-gray-300"
                          }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNew(!showNew)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer hover:opacity-75 transition-opacity"
                        aria-label="Toggle new password visibility"
                      >
                        {showNew ? <HidePassword /> : <ShowPassword />}
                      </button>
                      </div>
                      {passwordSameAsOld && (
                        <p className="text-red-500 text-xs mt-1">
                          New password should be different from current password
                        </p>
                      )}
                      {newPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {newPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    

                    <div className="relative">
                      <input
                        type={showConf ? "text" : "password"}
                        placeholder="Confirm New Password"
                        value={confirmPassword}
                        onChange={handleConfirmPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${confirmPasswordErrors.length > 0 || !passwordsMatch ? "border-red-300" : "border-gray-300"
                          }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConf(!showConf)}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700"
                        aria-label="Toggle confirm password visibility"
                      >
                        {showConf ? <HidePassword /> : <ShowPassword />}
                      </button>
                      </div>
                      {confirmPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {confirmPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                      {confirmPassword && !passwordsMatch && confirmPasswordErrors.length === 0 && (
                        <p className="text-red-500 text-xs mt-1">Passwords do not match</p>
                      )}

                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={handleCancelPasswordEdit}
                        className=" text-orange-600 px-4 py-1.5 rounded-full border border-orange-600 transition-colors cursor-pointer text-sm"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleChangePassword}
                        className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm"
                      >
                        Save
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-600">{password}</p>
                )}
              </div>
              {!isEditingPassword && (
                <button
                  type="button"
                  onClick={handleChangePassword}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Change
                </button>
              )}
            </div>

            {/* Notification Alerts */}
            <div className="flex justify-end">
              {!isEditMode ? (
                <button
                  type="button"
                  onClick={handleEditMode}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Edit
                </button>
              ) : (
                <>
                  <button
                    type="button"
                    onClick={handleEditMode}
                    className=" text-orange-600 px-4 py-1.5 rounded-full border border-orange-500 transition-colors cursor-pointer mr-2"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveProfile}
                    disabled={updateLoading}
                    className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer disabled:opacity-60"
                  >
                    {updateLoading ? "Saving..." : "Save"}
                  </button>
                </>
              )}
            </div>
          </div>
          <div className="relative">
            {/* Visual separator line for desktop */}
            <div className="hidden lg:block absolute left-0 top-0 bottom-0 w-px bg-gray-300 -ml-4"></div>


            {/* Address Management Section */}
            <div>
              {/* Default Shipping Address */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-800">Default shipping address</h3>
                  <button
                    type="button"
                    onClick={() => handleAddAddress("ship_to")}
                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                  >
                    Add New
                  </button>
                </div>
                <div className={`space-y-3 ${shippingAddresses.length > 3 ? 'max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100' : ''}`}>
                  {shippingAddresses.length === 0 ? (
                    <div className="text-gray-500 text-center py-4">No Address found</div>
                  ) : (
                    shippingAddresses.map((address) => (
                      <div key={address.id} className="border border-gray-200 rounded-md p-3 relative">
                        <div className="flex items-start">
                          <div className="mr-3 mt-1">
                            <input
                              type="radio"
                              name="default-shipping"
                              checked={address.id === defaultShippingId}
                              onChange={() => setDefaultAddress(address.id, "ship_to")}
                              className="h-4 w-4 text-orange-600 accent-orange-600 focus:ring-orange-500 border-gray-300"
                              title="Select as default shipping address"
                            />
                          </div>
                          <div className="w-full">
                            <p className="font-medium">{address.clinic_name}</p>
                            <p className="text-gray-600">{`${address.street_address}, (#${address.postal_code}), ${address.city}`}</p>
                            <p className="text-gray-600">{address.phone_number}</p>
                          </div>
                          <div className="flex space-x-2 ml-2">
                            <button
                              type="button"
                              onClick={() => handleDeleteAddress("shipping", address.id)}
                              className="text-red-500 cursor-pointer"
                              title="Delete shipping address"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                              >
                                <path
                                  d="M10.2499 2.00006C10.1291 1.99851 10.0098 2.02617 9.90195 2.08067C9.79413 2.13517 9.70108 2.2149 9.6307 2.31309C9.56032 2.41128 9.51469 2.52501 9.49771 2.64462C9.48072 2.76422 9.49288 2.88616 9.53314 3.00006H7.32025C6.39825 3.00006 5.54317 3.45817 5.03217 4.22467L3.84857 6.00006H3.74994C3.65056 5.99866 3.55188 6.01702 3.45966 6.05408C3.36743 6.09114 3.28349 6.14616 3.21271 6.21594C3.14194 6.28572 3.08573 6.36888 3.04737 6.46057C3.00901 6.55226 2.98926 6.65067 2.98926 6.75006C2.98926 6.84945 3.00901 6.94786 3.04737 7.03955C3.08573 7.13124 3.14194 7.2144 3.21271 7.28418C3.28349 7.35396 3.36743 7.40899 3.45966 7.44605C3.55188 7.48311 3.65056 7.50147 3.74994 7.50006H20.2499C20.3493 7.50147 20.448 7.48311 20.5402 7.44605C20.6324 7.40899 20.7164 7.35396 20.7872 7.28418C20.8579 7.2144 20.9141 7.13124 20.9525 7.03955C20.9909 6.94786 21.0106 6.84945 21.0106 6.75006C21.0106 6.65067 20.9909 6.55226 20.9525 6.46057C20.9141 6.36888 20.8579 6.28572 20.7872 6.21594C20.7164 6.14616 20.6324 6.09114 20.5402 6.05408C20.448 6.01702 20.3493 5.99866 20.2499 6.00006H20.1513L18.9677 4.22467C18.4567 3.45817 17.6011 3.00006 16.6796 3.00006H14.4667C14.507 2.88616 14.5192 2.76422 14.5022 2.64462C14.4852 2.52501 14.4396 2.41128 14.3692 2.31309C14.2988 2.2149 14.2057 2.13517 14.0979 2.08067C13.9901 2.02617 13.8707 1.99851 13.7499 2.00006H10.2499ZM4.48627 9.00006L5.56244 19.043C5.71244 20.444 6.88781 21.5001 8.29681 21.5001H15.7031C17.1116 21.5001 18.2869 20.444 18.4374 19.043L19.5136 9.00006H4.48627Z"
                                  fill="#FF0000"
                                />
                              </svg>
                            </button>
                            <button
                              type="button"
                              onClick={() => handleEditAddress(address)}
                              className="text-orange-500 cursor-pointer"
                              title="Edit shipping address"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                              >
                                <path
                                  d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z"
                                  fill="#EB6309"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Default Billing Address */}
              <div>
                <div className="flex justify-between items-center my-4">
                  <h3 className="text-lg font-medium text-gray-800">Default billing address</h3>
                  <button
                    type="button"
                    onClick={() => handleAddAddress("bill_to")}
                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                  >
                    Add New
                  </button>
                </div>
                <div className={`space-y-3 ${billingAddresses.length > 3 ? 'max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100' : ''}`}>
                  {billingAddresses.length === 0 ? (
                    <div className="text-gray-500 text-center py-4">No Address found</div>
                  ) : (
                    billingAddresses.map((address) => (
                      <div key={address.id} className="border border-gray-200 rounded-md p-3 relative">
                        <div className="flex items-start">
                          <div className="mr-3 mt-1">
                            <input
                              type="radio"
                              name="default-billing"
                              checked={address.id === defaultBillingId}
                              onChange={() => setDefaultAddress(address.id, "bill_to")}
                              className="h-4 w-4 text-orange-600 accent-orange-600 focus:ring-orange-500 border-gray-300"
                              title="Select as default billing address"
                            />
                          </div>
                          <div className="w-full">
                            <p className="font-medium">{address.clinic_name}</p>
                            <p className="text-gray-600">{`${address.street_address}, (#${address.postal_code}), ${address.city}`}</p>
                            <p className="text-gray-600">{address.phone_number}</p>
                          </div>
                          <div className="flex space-x-2 ml-2">
                            <button
                              type="button"
                              onClick={() => handleDeleteAddress("billing", address.id)}
                              className="text-red-500 cursor-pointer"
                              title="Delete billing address"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                              >
                                <path
                                  d="M10.2499 2.00006C10.1291 1.99851 10.0098 2.02617 9.90195 2.08067C9.79413 2.13517 9.70108 2.2149 9.6307 2.31309C9.56032 2.41128 9.51469 2.52501 9.49771 2.64462C9.48072 2.76422 9.49288 2.88616 9.53314 3.00006H7.32025C6.39825 3.00006 5.54317 3.45817 5.03217 4.22467L3.84857 6.00006H3.74994C3.65056 5.99866 3.55188 6.01702 3.45966 6.05408C3.36743 6.09114 3.28349 6.14616 3.21271 6.21594C3.14194 6.28572 3.08573 6.36888 3.04737 6.46057C3.00901 6.55226 2.98926 6.65067 2.98926 6.75006C2.98926 6.84945 3.00901 6.94786 3.04737 7.03955C3.08573 7.13124 3.14194 7.2144 3.21271 7.28418C3.28349 7.35396 3.36743 7.40899 3.45966 7.44605C3.55188 7.48311 3.65056 7.50147 3.74994 7.50006H20.2499C20.3493 7.50147 20.448 7.48311 20.5402 7.44605C20.6324 7.40899 20.7164 7.35396 20.7872 7.28418C20.8579 7.2144 20.9141 7.13124 20.9525 7.03955C20.9909 6.94786 21.0106 6.84945 21.0106 6.75006C21.0106 6.65067 20.9909 6.55226 20.9525 6.46057C20.9141 6.36888 20.8579 6.28572 20.7872 6.21594C20.7164 6.14616 20.6324 6.09114 20.5402 6.05408C20.448 6.01702 20.3493 5.99866 20.2499 6.00006H20.1513L18.9677 4.22467C18.4567 3.45817 17.6011 3.00006 16.6796 3.00006H14.4667C14.507 2.88616 14.5192 2.76422 14.5022 2.64462C14.4852 2.52501 14.4396 2.41128 14.3692 2.31309C14.2988 2.2149 14.2057 2.13517 14.0979 2.08067C13.9901 2.02617 13.8707 1.99851 13.7499 2.00006H10.2499ZM4.48627 9.00006L5.56244 19.043C5.71244 20.444 6.88781 21.5001 8.29681 21.5001H15.7031C17.1116 21.5001 18.2869 20.444 18.4374 19.043L19.5136 9.00006H4.48627Z"
                                  fill="#FF0000"
                                />
                              </svg>
                            </button>
                            <button
                              type="button"
                              onClick={() => handleEditAddress(address)}
                              className="text-orange-500 cursor-pointer"
                              title="Edit billing address"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                              >
                                <path
                                  d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z"
                                  fill="#EB6309"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <AddressModal
        modalOpen={addressModalOpen}
        modalType={addressModalType}
        addressType={addressType}
        modalData={addressModalData}
        handleAddressChange={handleAddressChange}
        handleAddressSubmit={handleAddressSubmit}
        setModalOpen={setAddressModalOpen}
      />
      <DeleteConfirmationModal
        confirmModalOpen={confirmModalOpen}
        addressToDelete={addressToDelete}
        cancelDeleteAddress={cancelDeleteAddress}
        confirmDeleteAddress={confirmDeleteAddress}
      />
    </>
  )
}

export default DrProfile