import React, { useState, useRef, useEffect } from "react";

interface PaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  totalPages,
  onPageChange,
  onItemsPerPageChange,
}) => {
  const [open, setOpen] = useState(false);
  const [openUpwards, setOpenUpwards] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const toggleDropdown = () => {
    if (!open) {
      const rect = dropdownRef.current?.getBoundingClientRect();
      if (rect) {
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;
        if (spaceBelow < 150 && spaceAbove > 150) {
          setOpenUpwards(true);
        } else {
          setOpenUpwards(false);
        }
      }
    }
    setOpen(!open);
  };

  const handleSelect = (value: number) => {
    onItemsPerPageChange(value);
    setOpen(false);
  };

  useEffect(() => {
    const closeOnOutsideClick = (e: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", closeOnOutsideClick);
    return () => document.removeEventListener("mousedown", closeOnOutsideClick);
  }, []);

  return (
    <div className="flex items-center justify-between my-3 mx-2">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2 relative" ref={dropdownRef}>
          <span className="text-sm text-gray-600">Rows per page:</span>
          <div
            onClick={toggleDropdown}
            className="relative border border-gray-300 rounded px-2 py-1 text-sm cursor-pointer bg-white"
          >
            {itemsPerPage}
          
          
          {open && (
            <ul
              className={`absolute left-[-1px] border py-1 border-gray-300 rounded bg-white shadow-md ${
                openUpwards ? "bottom-full mb-1" : "top-full mt-1"
              }`}
            >
              {[10, 20, 30, 50].map((num) => (
                <li
                  key={num}
                  onClick={() => handleSelect(num)}
                  className={`px-2 py-1 text-sm cursor-pointer hover:bg-orange-600 hover:text-gray-300 ${
                    itemsPerPage === num ? "bg-orange-600 text-gray-300 font-sm" : ""
                  }`}
                >
                  {num}
                </li>
              ))}
            </ul>
          )}
          </div>
        </div>

        {/* Display Range */}
        <div className="text-sm text-gray-600">
          Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
          {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems}{" "}
          entries
        </div>
      </div>

      {/* Right Section: Pagination Controls */}
      <div className="flex items-center space-x-1">
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === 1 ? "text-gray-400" : "text-[#EB6309] bg-[#FDE8D4]"
          }`}
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
        >
          «
        </button>
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === 1 ? "text-gray-400" : "text-[#EB6309] bg-[#FDE8D4]"
          }`}
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ‹
        </button>
        {Array.from({ length: totalPages }, (_, index) => index + 1).map(
          (page) => (
            <button
              key={page}
              className={`w-8 h-8 cursor-pointer text-center rounded-full text-sm ${
                currentPage === page
                  ? "bg-[#EB6309] text-white"
                  : "text-[#EB6309] bg-[#FDE8D4]"
              }`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          )
        )}
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === totalPages
              ? "text-gray-400"
              : "text-[#EB6309] bg-[#FDE8D4]"
          }`}
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          ›
        </button>
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === totalPages
              ? "text-gray-400"
              : "text-[#EB6309] bg-[#FDE8D4]"
          }`}
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          »
        </button>
      </div>
    </div>
  );
};

export default Pagination;
