"use client"

import type React from "react"

import Image from "next/image"
import { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import { getDecryptedToken, removeCookieByName } from "@/app/lib/auth"

interface HeaderProps {
  onSearchChange?: (value: string) => void
  searchValue?: string
}

const Header: React.FC<HeaderProps> = ({ onSearchChange, searchValue }) => {
  const pathname = usePathname()

  const [dashbordpath, setDashbordpath] = useState(false)
  const [firstname, setFirstname] = useState<string | null>(null)
  const [lastname, setLastname] = useState<string | null>(null)
  const [role, setRole] = useState<string | null>(null)
  const [profile_image, setProfileImage] = useState<string | null>(null)
  const [userName, setUserName] = useState<string | null>(null)
  const [userId, setUserId] = useState<string | null>(null)

  useEffect(() => {
    // Fetch data from cookies
    const fetchCookieData = () => {
      setFirstname(getDecryptedToken("first_name"))
      setLastname(getDecryptedToken("last_name"))
      setProfileImage(getDecryptedToken("profile_image"))
      setUserName(getDecryptedToken("username"))
      setUserId(getDecryptedToken("user_uuid"))
    }

    fetchCookieData()
    setRole(getDecryptedToken("Role"))

    const handleProfileUpdate = (event: CustomEvent) => {
      const { first_name, last_name, username, profile_image } = event.detail
      setFirstname(first_name)
      setLastname(last_name)
      setUserName(username)
      setProfileImage(profile_image)
    }

    window.addEventListener("profileUpdated", handleProfileUpdate as EventListener)

    if (pathname === "/dashboard") {
      setDashbordpath(true)
      removeCookieByName("patientId")
      removeCookieByName("LongcasePrescriptionData")
      removeCookieByName("ShortcasePrescriptionData")
      removeCookieByName("retainerData")
      removeCookieByName("patientData")
    } else {
      setDashbordpath(false)
    }

    return () => {
      window.removeEventListener("profileUpdated", handleProfileUpdate as EventListener)
    }
  }, [pathname])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    onSearchChange?.(value)
  }

  const avatarSrc = profile_image
    ? profile_image
    : `https://ui-avatars.com/api/?name=${firstname}+${lastname}?background=random?rounded=true`

  return (
    <header
      className={`flex items-center ${role !== "specialist" && role !== null ? "justify-between" : "justify-end"} bg-transparent px-2 py-2`}
    >
      {/* Greeting */}
      {role !== "specialist" && role !== null && (
        <div className="text-2xl font-medium text-gray-800">
          <div>Hi 👋</div>
        </div>
      )}
      <div className="flex items-center max-w-md">
        {dashbordpath && (
          <div className=" mx-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                value={searchValue}
                onChange={handleSearchChange}
                className="w-full pl-10 pr-4 py-3 rounded-full bg-white border focus:outline-none text-[#999] placeholder-[#999]"
              />
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M6.5 17.5L2 22"
                    stroke="#EB6309"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M4 11C4 6.02944 8.0294 2 13 2C17.9706 2 22 6.02944 22 11C22 15.9706 17.9706 20 13 20C8.0294 20 4 15.9706 4 11Z"
                    stroke="#EB6309"
                    strokeWidth="1.5"
                    strokeLinejoin="round"
                  />
                </svg>
              </span>
            </div>
          </div>
        )}

        {/* User Profile */}
        <div className="flex items-center bg-[#FFF] ps-1 pe-5 py-1 rounded-full space-x-2">
          <Image
            src={avatarSrc || "/placeholder.svg"}
            alt="User Avatar"
            width={1000}
            height={1000}
            className="w-12 h-12 rounded-full"
          />
          <div className="flex flex-col">
            <div>
              <p className="text-sm font-medium text-gray-800">{userName}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">ID#{userId}</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
