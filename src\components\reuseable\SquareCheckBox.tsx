import React, { useRef, useState } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import TickIcon from "./Icons/TickIcon";

interface CustomCheckboxProps {
  label: string;
  sup?: string;
  labelClass?: string;
  register?: UseFormRegisterReturn;
  error?: string;
  className?: string;
  id: string;
  value?: string;
  name?: string;
}

const SquareCheckBox: React.FC<CustomCheckboxProps> = ({
  label,
  register,
  error,
  className,
  id,
  value,
  labelClass,
  sup,
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [render, setRender] = useState<boolean>(false);

  const reRender = () => {
    setRender((prev) => !prev);
  };
  return (
    <div className="flex items-center gap-1">
      {false && render && <span></span>}
      <label
        htmlFor={id}
        className={` flex items-center gap-1.5 cursor-pointer`}
      >
        <input
          onClick={reRender}
          value={value}
          type="checkbox"
          id={id}
          {...register}
          className="peer hidden accent-orange-600"
          ref={(el) => {
            register?.ref?.(el);
            inputRef.current = el;
          }}
        />
        <div
          className={`w-5 h-5 appearance-none border flex justify-center items-center rounded-sm border-gray peer-checked:bg-primary peer-checked:border-transparent ${className}`}
        >
          {inputRef.current?.checked && (
            <TickIcon fill="#FFFFFF" classess="w-3 h-3" />
          )}
        </div>
        <span className={`${labelClass} text-gray`}>
          {label}
          <sup>{sup}</sup>
        </span>
      </label>

      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default SquareCheckBox;
