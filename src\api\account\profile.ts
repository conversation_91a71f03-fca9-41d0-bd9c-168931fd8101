
import { API_ROUTES, API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import {
  DoctorProfileApiResponse,
  ProfileData,
  UpdateDoctorProfilePayload,
  UpdateProfileResponse,
} from "@/types/types";
import { toast } from "react-toastify";
import { getDecryptedToken } from "@/app/lib/auth";

// ✅ Fetch Doctor Profile
export const fetchDoctorProfile = async (): Promise<DoctorProfileApiResponse> => {
  const url = API_SERVER_ROUTES.PROFILE.GET_PROFILE;
  try {
    const token = await getDecryptedToken("AccessToken");

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle non-200 responses (e.g., 401, 500)
      console.error("API Error:", data);
      return {
        status: response.status,
        success: false,
        profile_image: "",
        email: "",
        last_name: "",
        first_name: "",
        username: "",
        data: {} as ProfileData,
        message: data.message || "Failed to fetch profile",
      };
    }

    return {
      status: response.status,
      success: true,
      profile_image: data.profile_image || "",
      email: data.email || "",
      last_name: data.last_name || "",
      first_name: data.first_name || "",
      username: data.username || "",
      data: data.data,
      message: data.message || "Profile fetched successfully",
    };
  } catch (error) {
    console.error("Fetch Error:", error);
    return {
      status: 500,
      success: false,
      profile_image: "",
      email: "",
      last_name: "",
      first_name: "",
      username: "",
      data: {} as ProfileData,
      message: "An unexpected error occurred",
    };
  }
};

// ✅ Update Doctor Profile with Toast Notifications
export const updateDoctorProfile = async (
  token: string,
  payload: UpdateDoctorProfilePayload,
): Promise<UpdateProfileResponse | null> => {
  let responseData: UpdateProfileResponse | null = null;

  try {
    if (!token) {
      toast.error("Authorization token is missing");
      return null;
    }

    const formData = new FormData();
    formData.append("first_name", payload.first_name);
    formData.append("last_name", payload.last_name);
    formData.append("email", payload.email);
    formData.append("username", payload.username);
    if (payload.profileImage) {
      formData.append("profile_image", payload.profileImage);
    }

    const response = await fetch(API_ROUTES.PROFILE.UPDATE_PROFILE, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    responseData = await response.json();

    if (!response.ok) {
      const message =
        responseData?.message ||
        `Failed to update profile (${response.statusText})`;
      toast.error(message);
      return responseData;
    }

    toast.success(responseData?.message || "Profile updated successfully");
    return responseData;
  } catch (err) {
    console.error("Error updating profile:", err);
    return responseData; // can be null if request failed before parsing
  }
};

