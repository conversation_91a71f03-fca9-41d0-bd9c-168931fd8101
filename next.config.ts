import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async redirects() {
    return [
      {
        source: "/",
        destination: "/login",
        permanent: true,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ui-avatars.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '************',
        port: '8000',
      },
      {
        protocol: 'https',
        hostname: 'orthodontic.desinoir.com',
      },
      {
        protocol: 'https',
        hostname: 'orthodonticbackend.desinoir.com',
      },
      {
        protocol: 'https',
        hostname: 'orthodontivc-20250724-oss.oss-me-central-1.aliyuncs.com',
      },
      {
        protocol: 'https',
        hostname: 'dev.api.4dgraphy.com',
      },
      {
        protocol: 'https',
        hostname: 'orthodontivc-20250724-oss.oss-sa-east-1.aliyuncs.com',
      },
    ],
  },
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
          {
            key: "Access-Control-Allow-Origin",
            value: "https://downloads-default.nemocloud-services.com",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET,OPTIONS,PATCH,DELETE,POST,PUT",
          },
          {
            key: "Access-Control-Allow-Headers",
            value:
              "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
