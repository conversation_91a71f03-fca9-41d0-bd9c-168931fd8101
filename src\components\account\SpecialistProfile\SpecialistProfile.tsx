"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { toast } from "react-toastify"
import { getDecryptedToken, setEncryptedToken } from "@/app/lib/auth"
import { updateSpecialistProfile, deleteSpecialistProfileImage } from "@/api/account/specialist"

import Image from "next/image"
import CryptoJS from "crypto-js"
import type { ProfileData, SpecialistProfileApiResponse } from "@/types/types"
import { changePassword, fetchDoctorProfile } from "@/utils/ApisHelperFunction"
import HidePassword from "@/components/reuseable/Icons/HidePassword"
import ShowPassword from "@/components/reuseable/Icons/ShowPassword"

const SECRET_KEY = "QkNw7X+dzf1qvOYY6HgLZ9uK8bEN9kZLb8S8FZGhRZc"

export function encryptTableName(tableName: string): string {
  return CryptoJS.AES.encrypt(tableName, SECRET_KEY).toString()
}

interface SpecialistProfileProps {
  initialData?: SpecialistProfileApiResponse | null
  error?: string | null
}

const validatePassword = (password: string) => {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/[0-9]/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    errors.push("Password must contain at least one special character")
  }

  return errors
}

const SpecialistProfile: React.FC<SpecialistProfileProps> = ({ initialData, error: serverError }) => {
  const [username, setUsername] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [password] = useState("*************")
  const [email, setEmail] = useState("")

  const [isEditMode, setIsEditMode] = useState(false)
  const [editUsername, setEditUsername] = useState("")
  const [editFirstName, setEditFirstName] = useState("")
  const [editLastName, setEditLastName] = useState("")
  const [editEmail, setEditEmail] = useState("")

  const [profileLoading, setProfileLoading] = useState(!initialData)
  const [profileError, setProfileError] = useState<string | null>(serverError || null)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [profileImage, setProfileImage] = useState<string>("")
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>("")
  const id = Number(getDecryptedToken("userid"))
  const [userId, setUserId] = useState<number | null>(id)
  const [isEditingPassword, setIsEditingPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  const [newPasswordErrors, setNewPasswordErrors] = useState<string[]>([])
  const [confirmPasswordErrors, setConfirmPasswordErrors] = useState<string[]>([])
  const [passwordsMatch, setPasswordsMatch] = useState(true)
  const [passwordSameAsOld, setPasswordSameAsOld] = useState(false)
  const [wrongCurrentError, setWrongCurrentError] = useState("");
  const [showCurr, setShowCurr] = useState(false);
  const [showNew, setShowNew] = useState(false);
  const [showConf, setShowConf] = useState(false);

  const fetchProfile = async () => {
    // If we have server data, use it first
    if (initialData) {
      setUsername(initialData.username || "")
      setFirstName(initialData.first_name || "")
      setLastName(initialData.last_name || "")
      setEmail(initialData.email || "")
      setProfileImage(initialData.profile_image || "")
      setImagePreview("")
      setEditUsername(initialData.username || "")
      setEditFirstName(initialData.first_name || "")
      setEditLastName(initialData.last_name || "")
      setEditEmail(initialData.email || "")
      setProfileLoading(false)
      return
    }

    // Fallback to client-side fetch if no server data
    setProfileLoading(true)
    setProfileError(null)
    setUserId(Number(getDecryptedToken("userid")))
    const token = getDecryptedToken("AccessToken")
    if (token) {
      const profileResponse: ProfileData | null = await fetchDoctorProfile(token)
      if (profileResponse) {
        setUsername(profileResponse.username || "")
        setFirstName(profileResponse.first_name || "")
        setLastName(profileResponse.last_name || "")
        setEmail(profileResponse.email || "")
        setProfileImage(profileResponse.profile_image || "")
        setImagePreview("")
        setEditUsername(profileResponse.username || "")
        setEditFirstName(profileResponse.first_name || "")
        setEditLastName(profileResponse.last_name || "")
        setEditEmail(profileResponse.email || "")
      } else {
        setProfileError("Failed to fetch specialist profile.")
      }
      setProfileLoading(false)
    }
  }

  console.log("userid", userId)
  useEffect(() => {
    fetchProfile()
  }, [initialData])

  const handleEditMode = () => {
    if (isEditMode) {
      // Cancel edit mode - revert changes
      setEditUsername(username)
      setEditFirstName(firstName)
      setEditLastName(lastName)
      setEditEmail(email)
      setSelectedImage(null)
      setImagePreview("")
      const fileInput = document.getElementById("profile-image") as HTMLInputElement
      if (fileInput) fileInput.value = ""
    } else {
      // Enter edit mode - set edit values to current values
      setEditUsername(username)
      setEditFirstName(firstName)
      setEditLastName(lastName)
      setEditEmail(email)
    }
    setIsEditMode(!isEditMode)
  }

 const handleSaveProfile = async () => {
  if (!editFirstName.trim() || !editLastName.trim() || !editUsername.trim() || !editEmail.trim()) {
    toast.error("All fields are required.")
    return
  }

  setUpdateLoading(true)
  try {
    const token = getDecryptedToken("AccessToken")
    if (!token) {
      toast.error("Authentication token missing.")
      setUpdateLoading(false)
      return
    }

    const updatedProfile: File | null = selectedImage ?? null

    const result = await updateSpecialistProfile(token, {
      first_name: editFirstName,
      last_name: editLastName,
      email: editEmail,
      username: editUsername,
      profileImage: updatedProfile,
    })

    if (result && result.data) {
      console.log("Specialist profile updated:", result.data)

      // ✅ Update local state
      setFirstName(editFirstName)
      setLastName(editLastName)
      setUsername(editUsername)
      setEmail(editEmail)

      // ✅ Save text data in local storage
      setEncryptedToken("first_name", editFirstName)
      setEncryptedToken("last_name", editLastName)
      setEncryptedToken("username", editUsername)

      // ✅ If backend returns a new image, update with that
      if (result.data.profile_image) {
        setEncryptedToken("profile_image", result.data.profile_image)
        setProfileImage(result.data.profile_image)
      }

      // ✅ Fire global update event
      window.dispatchEvent(
        new CustomEvent("profileUpdated", {
          detail: {
            first_name: editFirstName,
            last_name: editLastName,
            username: editUsername,
            profile_image: result.data.profile_image || profileImage,
          },
        }),
      )

      toast.success("Profile updated successfully.")
    } else {
      toast.error("Failed to update profile.")
    }

    setIsEditMode(false)
    setSelectedImage(null)
    setImagePreview("")
  } catch (err) {
    console.error("Error updating specialist profile:", err)
    toast.error("Profile update failed!")
  }
  setUpdateLoading(false)
}

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
      if (!validTypes.includes(file.type)) {
        toast.error("Please select a valid image file (JPEG, PNG, GIF, WebP)")
        return
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB")
        return
      }

      setSelectedImage(file)

      // Create preview URL
      const reader = new FileReader()
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = async () => {
    if (profileImage && userId) {
      // Extract file key from profile image URL
      let fileKey = profileImage

      // If it's a full URL, extract just the path after the domain
      if (profileImage.startsWith("http")) {
        try {
          const url = new URL(profileImage)
          fileKey = url.pathname.substring(1) // Remove leading slash
        } catch (error) {
          console.log("[v0] Error parsing URL, using original:", error)
          // Fallback: try to extract everything after the last domain part
          const parts = profileImage.split("/")
          const domainIndex = parts.findIndex(
            (part) => part.includes(".com") || part.includes(".net") || part.includes(".org"),
          )
          if (domainIndex !== -1 && domainIndex < parts.length - 1) {
            fileKey = parts.slice(domainIndex + 1).join("/")
          }
        }
      }

      console.log("[v0] Extracted fileKey:", fileKey)

      const success = await deleteSpecialistProfileImage(userId, fileKey)

      if (success) {
        // Clear the profile image from state
        setProfileImage("")
        setEncryptedToken("profile_image", "")

        window.dispatchEvent(
          new CustomEvent("profileUpdated", {
            detail: {
              first_name: firstName,
              last_name: lastName,
              username: username,
              profile_image: "",
            },
          }),
        )
        // Note: Success toast is already handled in the API function
      } else {
        // Note: Error toast is already handled in the API function
        return
      }
    }

    // Clear local preview and selected image
    setSelectedImage(null)
    setImagePreview("")
    // Reset file input
    const fileInput = document.getElementById("profile-image") as HTMLInputElement
    if (fileInput) {
      fileInput.value = ""
    }
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    await handleSaveProfile()
  }

  const handleCancelPasswordEdit = () => {
    setCurrentPassword("")
    setNewPassword("")
    setConfirmPassword("")
    setNewPasswordErrors([])
    setConfirmPasswordErrors([])
    setPasswordsMatch(true)
    setPasswordSameAsOld(false)
    setIsEditingPassword(false)
  }

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setNewPassword(password)
    setNewPasswordErrors(validatePassword(password))

    setPasswordSameAsOld(Boolean(currentPassword && password === currentPassword))

    if (confirmPassword) {
      setPasswordsMatch(password === confirmPassword)
    }
  }

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setConfirmPassword(password)
    setConfirmPasswordErrors(validatePassword(password))
    setPasswordsMatch(newPassword === password)
  }

   const handleChangePassword = async () => {
     if (isEditingPassword) {
       setWrongCurrentError("");
       if (!currentPassword.trim() || !newPassword.trim() || !confirmPassword.trim()) {
         toast.error("All password fields are required.")
         return
       }
 
       if (passwordSameAsOld) {
         return
       }
 
       if (newPasswordErrors.length > 0 || confirmPasswordErrors.length > 0) {
         toast.error("Please fix password validation errors before submitting.")
         return
       }
 
       if (!passwordsMatch) {
         toast.error("New password and confirm password do not match.")
         return
       }
 
       const success = await changePassword(currentPassword, newPassword, confirmPassword)
 
       if (success) {
         setCurrentPassword("")
         setNewPassword("")
         setConfirmPassword("")
         setNewPasswordErrors([])
         setConfirmPasswordErrors([])
         setPasswordsMatch(true)
         setPasswordSameAsOld(false)
         setIsEditingPassword(false)
       }
       else {
         setWrongCurrentError("Current password is incorrect");
       }
     } else {
       setIsEditingPassword(true)
     }
   }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      {profileLoading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : profileError ? (
        <div className="text-red-500 text-center py-4">{profileError}</div>
      ) : (
        <form onSubmit={handleUpdateProfile} className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-800">Profile</h2>
            </div>

            {/* Profile Image Section */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700 mb-2">Profile Image</p>
                <div className="flex items-center justify-between">
                  {/* Image Preview */}
                  <div className="w-20 h-20 rounded-full border-2 border-gray-300 overflow-hidden bg-gray-100 flex items-center justify-center">
                    {imagePreview || profileImage ? (
                      <Image
                        src={imagePreview || profileImage}
                        alt="Profile"
                        width={1000}
                        height={1000}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    )}
                  </div>

                  {isEditMode && (
                    <div className="flex items-center gap-2">
                      <label
                        htmlFor="profile-image"
                        className="bg-[#EB6309] margin-[0px] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm text-center"
                      >
                        {imagePreview || profileImage ? "Change Image" : "Upload Image"}
                      </label>
                      <input
                        id="profile-image"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      {(imagePreview || selectedImage) && (
                        <button
                          type="button"
                          onClick={removeImage}
                          className="text-red-500 text-sm hover:text-red-700 transition-colors"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Profile Fields */}
            {[
              {
                label: "First Name",
                value: firstName,
                editValue: editFirstName,
                setEditValue: setEditFirstName,
              },
              {
                label: "Last Name",
                value: lastName,
                editValue: editLastName,
                setEditValue: setEditLastName,
              },
              {
                label: "Username",
                value: username,
                editValue: editUsername,
                setEditValue: setEditUsername,
              },
              {
                label: "Primary Account Email*",
                value: email,
                editValue: editEmail,
                setEditValue: setEditEmail,
              },
            ].map((field) => (
              <div key={field.label} className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div className="w-full">
                  <p className="font-medium text-gray-700">{field.label}</p>
                  {isEditMode ? (
                    <input
                      type="text"
                      value={field.editValue}
                      onChange={(e) => field.setEditValue(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />
                  ) : (
                    <p className="text-gray-600">{field.value}</p>
                  )}
                </div>
              </div>
            ))}

            {/* Password Field - Keep independent password section */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Password</p>
                {isEditingPassword ? (
                  <div className="space-y-3 mt-2">
                    <div className="relative">
                    <input
                      type={showCurr ? "text" : "password"}
                      placeholder="Current Password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />
                    <button
                      type="button"
                      onClick={() => setShowCurr(!showCurr)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer hover:opacity-75 transition-opacity"
                      aria-label="Toggle current password visibility"
                    >
                      {showCurr ? <HidePassword /> : <ShowPassword />}
                    </button>
                    </div>
                    {wrongCurrentError && (
                      <p className="text-red-500 text-xs mt-1">{wrongCurrentError}</p>
                    )}
                
                    <div className="relative">
                      <input
                        type={showNew ? "text" : "password"}
                        placeholder="New Password"
                        value={newPassword}
                        onChange={handleNewPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${newPasswordErrors.length > 0 || passwordSameAsOld ? "border-red-300" : "border-gray-300"
                          }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNew(!showNew)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 cursor-pointer hover:opacity-75 transition-opacity"
                        aria-label="Toggle new password visibility"
                      >
                        {showNew ? <HidePassword /> : <ShowPassword />}
                      </button>
                      </div>
                      {passwordSameAsOld && (
                        <p className="text-red-500 text-xs mt-1">
                          New password should be different from current password
                        </p>
                      )}
                      {newPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {newPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    

                    <div className="relative">
                      <input
                        type={showConf ? "text" : "password"}
                        placeholder="Confirm New Password"
                        value={confirmPassword}
                        onChange={handleConfirmPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${confirmPasswordErrors.length > 0 || !passwordsMatch ? "border-red-300" : "border-gray-300"
                          }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConf(!showConf)}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700"
                        aria-label="Toggle confirm password visibility"
                      >
                        {showConf ? <HidePassword /> : <ShowPassword />}
                      </button>
                      </div>
                      {confirmPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {confirmPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                      {confirmPassword && !passwordsMatch && confirmPasswordErrors.length === 0 && (
                        <p className="text-red-500 text-xs mt-1">Passwords do not match</p>
                      )}

                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={handleCancelPasswordEdit}
                        className=" text-orange-600 px-4 py-1.5 rounded-full border border-orange-600 transition-colors cursor-pointer text-sm"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleChangePassword}
                        className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm"
                      >
                        Save
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-600">{password}</p>
                )}
              </div>
              {!isEditingPassword && (
                <button
                  type="button"
                  onClick={handleChangePassword}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Change
                </button>
              )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
              {!isEditMode ? (
                <button
                  type="button"
                  onClick={handleEditMode}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Edit
                </button>
              ) : (
                <>
                  <button
                    type="button"
                    onClick={handleSaveProfile}
                    disabled={updateLoading}
                    className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer disabled:opacity-60"
                  >
                    {updateLoading ? "Saving..." : "Save"}
                  </button>
                  <button
                    type="button"
                    onClick={handleEditMode}
                    className="bg-gray-500 text-white px-4 py-1.5 rounded-full hover:bg-gray-600 transition-colors cursor-pointer"
                  >
                    Cancel
                  </button>
                </>
              )}
            </div>
          </div>
        </form>
      )}
    </div>
  )
}

export default SpecialistProfile
